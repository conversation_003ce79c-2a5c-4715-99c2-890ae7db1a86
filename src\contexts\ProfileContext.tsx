import React, { createContext, useContext, useState, useEffect } from 'react';

interface ProfileData {
  name: string;
  email: string;
  phone: string;
  jobTitle: string;
  department: string;
  profileImage: string;
}

interface ProfileContextType {
  profileData: ProfileData;
  updateProfileData: (data: Partial<ProfileData>) => void;
  updateProfileImage: (imageUrl: string) => void;
  getInitials: (name: string) => string;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

const PROFILE_STORAGE_KEY = 'dr-kumar-profile-data';

export const ProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [profileData, setProfileData] = useState<ProfileData>(() => {
    // Try to load from localStorage first
    const stored = localStorage.getItem(PROFILE_STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error('Error parsing stored profile data:', error);
      }
    }
    
    // Default profile data
    return {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+91 **********",
      jobTitle: "Super Admin",
      department: "Operations",
      profileImage: "",
    };
  });

  // Save to localStorage whenever profileData changes
  useEffect(() => {
    localStorage.setItem(PROFILE_STORAGE_KEY, JSON.stringify(profileData));
  }, [profileData]);

  const updateProfileData = (data: Partial<ProfileData>) => {
    setProfileData(prev => ({ ...prev, ...data }));
  };

  const updateProfileImage = (imageUrl: string) => {
    setProfileData(prev => ({ ...prev, profileImage: imageUrl }));
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const value: ProfileContextType = {
    profileData,
    updateProfileData,
    updateProfileImage,
    getInitials,
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};
