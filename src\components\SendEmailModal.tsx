
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Mail, Search, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';

interface Customer {
  name: string;
  email: string;
  phone?: string;
  city?: string;
}

interface SendEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  customers: Customer[];
}

const SendEmailModal: React.FC<SendEmailModalProps> = ({ isOpen, onClose, customers }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomers, setSelectedCustomers] = useState<Customer[]>([]);
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const { toast } = useToast();

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectCustomer = (customer: Customer) => {
    if (!selectedCustomers.find(c => c.email === customer.email)) {
      setSelectedCustomers([...selectedCustomers, customer]);
    }
    setSearchTerm('');
  };

  const handleRemoveCustomer = (customerEmail: string) => {
    setSelectedCustomers(selectedCustomers.filter(c => c.email !== customerEmail));
  };

  const handleSendNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedCustomers.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please select at least one customer to send the notification",
        variant: "destructive",
      });
      return;
    }

    if (!subject.trim() || !message.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both subject and message",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: "Email Sent Successfully",
        description: `Notification sent to ${selectedCustomers.length} customer(s)`,
      });

      // Reset form
      setSelectedCustomers([]);
      setSubject('');
      setMessage('');
      onClose();
      
    } catch (error) {
      toast({
        title: "Send Failed",
        description: "Failed to send notification. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleClose = () => {
    setSelectedCustomers([]);
    setSubject('');
    setMessage('');
    setSearchTerm('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Send Email Notification
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSendNotification} className="space-y-6">
          {/* Notification Type */}
          <div className="space-y-2">
            <Label>Notification Type</Label>
            <div className="flex gap-2">
              <Badge variant="default" className="bg-blue-100 text-blue-700">
                Email
              </Badge>
            </div>
          </div>

          {/* Recipient Search */}
          <div className="space-y-2">
            <Label>Select Recipients</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search customers by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Search Results */}
            {searchTerm && filteredCustomers.length > 0 && (
              <div className="max-h-32 overflow-y-auto border rounded-md bg-white dark:bg-gray-800">
                {filteredCustomers.slice(0, 5).map((customer) => (
                  <button
                    key={customer.email}
                    type="button"
                    onClick={() => handleSelectCustomer(customer)}
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 border-b last:border-b-0"
                  >
                    <div className="font-medium text-sm">{customer.name}</div>
                    <div className="text-xs text-gray-500">{customer.email}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Selected Recipients */}
          {selectedCustomers.length > 0 && (
            <div className="space-y-2">
              <Label>Selected Recipients ({selectedCustomers.length})</Label>
              <div className="flex flex-wrap gap-2 max-h-24 overflow-y-auto">
                {selectedCustomers.map((customer) => (
                  <Badge
                    key={customer.email}
                    variant="secondary"
                    className="flex items-center gap-1 pr-1"
                  >
                    <span className="truncate max-w-[120px]">{customer.name}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveCustomer(customer.email)}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5 transition-colors duration-200"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Enter email subject"
              required
            />
          </div>

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your message here..."
              rows={4}
              required
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSending || selectedCustomers.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSending ? 'Sending...' : 'Send Notification'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SendEmailModal;
