# Dr. Kumar Laboratories Admin Dashboard

A modern, responsive admin dashboard built with React and Vite for managing Dr. Kumar Laboratories operations. This comprehensive management system provides a complete solution for laboratory administration, inventory management, customer relations, and staff coordination.

## 🎯 Project Overview

**Dr. Kumar Laboratories Admin Dashboard** is a full-featured administrative interface designed specifically for laboratory management. The application provides real-time analytics, comprehensive data management, and streamlined workflows for laboratory operations.

### Main Features

- **📊 Dashboard Analytics**: Real-time metrics, KPIs, and interactive charts
- **🧪 Product Management**: Complete CRUD operations for laboratory products and inventory
- **📦 Order Management**: Track orders, manage fulfillment, and monitor payment status
- **👥 Customer Management**: Comprehensive customer database with order history
- **👨‍💼 Staff Management**: Employee profiles, role-based access, and team coordination
- **📋 Inventory Tracking**: Real-time stock monitoring and automated alerts
- **📈 Reports & Analytics**: Detailed reporting with interactive charts and graphs
- **👤 Profile Management**: User profiles with photo upload and role-based permissions
- **🌓 Theme Support**: Dark/Light mode with smooth transitions
- **📱 Responsive Design**: Mobile-first design that works on all devices
- **🔍 Advanced Search**: Intelligent search with filtering and suggestions
- **🎫 Coupon Management**: Create and manage promotional coupons
- **📝 Consultant Management**: Patient management and appointment tracking

## 🛠️ Tech Stack

### Core Technologies

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite (Fast build tool and dev server)
- **Styling**: TailwindCSS with custom design system
- **UI Components**: Radix UI primitives for accessibility
- **Icons**: Lucide React (Modern icon library)
- **Charts & Analytics**: Recharts for data visualization

### Additional Libraries

- **Forms & Validation**: React Hook Form with Zod schema validation
- **Routing**: React Router DOM v6
- **State Management**: React Context API with custom hooks
- **Date Handling**: date-fns for date manipulation
- **Image Processing**: react-image-crop for profile photos
- **Notifications**: Sonner for toast notifications
- **Animations**: TailwindCSS Animate for smooth transitions

### Development Tools

- **TypeScript**: Full type safety and better developer experience
- **ESLint**: Code linting and quality assurance
- **PostCSS**: CSS processing and optimization
- **Autoprefixer**: Automatic vendor prefixing

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18.0.0 or higher)
- **npm** (version 8.0.0 or higher) or **yarn** package manager
- **Git** for version control

### Installation Steps

1. **Clone the repository**:

```bash
git clone <repository-url>
cd dr-kumar-laboratories-admin
```

2. **Install dependencies**:

```bash
npm install
# or
yarn install
```

3. **Start the development server**:

```bash
npm run dev
# or
yarn dev
```

4. **Open your browser** and navigate to `http://localhost:8080`

### Available Scripts

| Command              | Description                              |
| -------------------- | ---------------------------------------- |
| `npm run dev`        | Start development server with hot reload |
| `npm run build`      | Build optimized production bundle        |
| `npm run start`      | Preview production build locally         |
| `npm run lint`       | Run ESLint for code quality checks       |
| `npm run type-check` | Run TypeScript type checking             |

## 📁 Project Folder Structure

```
dr-kumar-laboratories-admin/
├── public/                     # Static assets
│   ├── favicon.ico            # Application favicon
│   ├── placeholder.svg        # Placeholder images
│   └── robots.txt            # SEO robots file
├── src/                       # Source code
│   ├── components/           # React components
│   │   ├── ui/              # Base UI components (Button, Input, Dialog, etc.)
│   │   ├── Dashboard.tsx    # Main dashboard with analytics
│   │   ├── Products.tsx     # Product management interface
│   │   ├── Orders.tsx       # Order management system
│   │   ├── Customers.tsx    # Customer management
│   │   ├── StaffManagement.tsx # Staff and team management
│   │   ├── Inventory.tsx    # Inventory tracking
│   │   ├── CouponsReferral.tsx # Coupon management
│   │   ├── Consultant.tsx   # Patient/consultant management
│   │   ├── ProfilePage.tsx  # User profile management
│   │   ├── ImageCropModal.tsx # Profile photo cropping
│   │   └── ...             # Other specialized components
│   ├── contexts/            # React Context providers
│   │   └── ProfileContext.tsx # User profile state management
│   ├── hooks/              # Custom React hooks
│   │   └── use-toast.ts    # Toast notification hook
│   ├── lib/                # Utility libraries and configurations
│   │   └── utils.ts        # Common utility functions
│   ├── constants/          # Application constants and data
│   │   └── products.ts     # Product categories and sample data
│   ├── utils/              # Helper functions
│   ├── App.tsx             # Main application component with routing
│   ├── main.tsx           # Application entry point
│   ├── index.css          # Global styles and Tailwind imports
│   └── vite-env.d.ts      # Vite environment type definitions
├── dist/                   # Production build output (generated)
├── node_modules/          # Dependencies (generated)
├── components.json        # Shadcn/ui component configuration
├── eslint.config.js       # ESLint configuration
├── index.html            # HTML template
├── package.json          # Project dependencies and scripts
├── postcss.config.js     # PostCSS configuration
├── tailwind.config.ts    # TailwindCSS configuration
├── tsconfig.json         # TypeScript configuration
├── vite.config.ts        # Vite build configuration
└── README.md             # Project documentation (this file)
```

## 🔗 Backend Integration Guidelines

This application is currently built with static data and is ready for backend integration. The following sections indicate where backend functionality should be implemented:

### API Integration Points

Throughout the codebase, you'll find `TODO` comments indicating where backend integration is needed:

```typescript
// TODO: Add API integration here to fetch dynamic data from the backend
// TODO: Implement error handling for API failures or missing data
// TODO: Replace static content with dynamic backend values
```

### Key Integration Areas

1. **Authentication & Authorization**

   - User login/logout functionality
   - Role-based access control
   - Session management

2. **Data Management**

   - Product CRUD operations
   - Order management and tracking
   - Customer data synchronization
   - Staff management and permissions

3. **Real-time Features**

   - Live dashboard metrics
   - Inventory updates
   - Order status changes
   - Notification system

4. **File Management**
   - Profile photo uploads
   - Product image management
   - Document attachments

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

## 📄 License

This project is proprietary software for Dr. Kumar Laboratories. All rights reserved.

## 🆘 Support

For support and questions:

- **Technical Issues**: Create an issue in the repository
- **Feature Requests**: Contact the development team
- **Documentation**: Check this README and inline code comments

---

**Built with ❤️ for Dr. Kumar Laboratories**

_A modern, efficient, and user-friendly admin dashboard for laboratory management._
