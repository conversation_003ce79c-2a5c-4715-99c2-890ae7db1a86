import React from "react";

interface AnimatedTextProps {
  children: React.ReactNode;
  className?: string;
  variant?: "primary" | "secondary" | "highlight";
}

const AnimatedText: React.FC<AnimatedTextProps> = ({
  children,
  className = "",
  variant = "primary",
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case "primary":
        return "text-gray-900 dark:text-white";
      case "secondary":
        return "text-gray-700 dark:text-gray-200";
      case "highlight":
        return "text-black dark:text-white";
      default:
        return "text-gray-900 dark:text-white";
    }
  };

  return (
    <span
      className={`transition-colors duration-500 ease-in-out ${getVariantClasses()} ${className}`}
    >
      {children}
    </span>
  );
};

export default AnimatedText;
