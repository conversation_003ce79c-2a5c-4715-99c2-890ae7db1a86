import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const SignIn = () => {
  const [isAdmin, setIsAdmin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [keepLoggedIn, setKeepLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate authentication
    setTimeout(() => {
      if (email && password) {
        if (isAdmin) {
          // Admin requires 2FA
          navigate("/auth/two-factor", {
            state: { email, role: "Admin" },
          });
        } else {
          // Employee goes directly to dashboard
          navigate("/dashboard");
        }
        toast({
          title: "Sign in successful",
          description: `Welcome back, ${isAdmin ? "Admin" : "Employee"}!`,
        });
      } else {
        toast({
          title: "Error",
          description: "Please enter valid credentials",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md space-y-6">
          {/* Logo and Brand */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img
                src="/lovable-uploads/079f933a-4d0a-4e30-bf46-582b2b7fd97b.png"
                alt="Dr. Kumar Laboratories"
                className="w-20 h-20 object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold text-black">
              Dr. Kumar Laboratories – Sign In
            </h1>
          </div>

          <div className="space-y-2">
            <h2 className="text-3xl font-bold text-gray-900">Sign In</h2>
            <p className="text-gray-600">
              Enter your email and password to sign in!
            </p>
          </div>

          {/* Role Toggle */}
          <div className="flex items-center justify-center gap-4 p-4 bg-white rounded-lg border">
            <span
              className={`text-sm font-medium ${
                !isAdmin ? "text-gray-900" : "text-gray-500"
              }`}
            >
              Employee
            </span>
            <Switch
              checked={isAdmin}
              onCheckedChange={setIsAdmin}
              className="data-[state=checked]:bg-[#FFD700]"
            />
            <span
              className={`text-sm font-medium ${
                isAdmin ? "text-gray-900" : "text-gray-500"
              }`}
            >
              Admin
            </span>
          </div>

          {/* Sign In Form */}
          <form onSubmit={handleSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">
                Email<span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                className="h-12 bg-white text-black border-gray-300"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">
                Password<span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                  className="h-12 pr-10 bg-white text-black border-gray-300"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="keep-logged-in"
                  checked={keepLoggedIn}
                  onCheckedChange={(checked) =>
                    setKeepLoggedIn(checked === true)
                  }
                />
                <Label
                  htmlFor="keep-logged-in"
                  className="text-sm text-gray-600"
                >
                  Keep me logged in
                </Label>
              </div>
              <Link
                to="/auth/forgot-password"
                className="text-sm text-[#FFD700] hover:underline"
              >
                Forgot password?
              </Link>
            </div>

            <Button
              type="submit"
              className="w-full h-12 bg-[#FFD700] hover:bg-[#E6C200] text-black"
              disabled={isLoading}
            >
              {isLoading ? "Signing In..." : "Sign In"}
            </Button>
          </form>

          <p className="text-center text-sm text-gray-600">
            Don't have an account?{" "}
            <Link
              to="/auth/signup"
              className="text-[#FFD700] hover:underline font-medium"
            >
              Sign Up
            </Link>
          </p>
        </div>
      </div>

      {/* Right Side - Background Image */}
      <div className="hidden lg:block lg:w-1/2 relative">
        <img
          src="/lovable-uploads/********-1ce5-4e40-ad4e-f2133eca9b94.png"
          alt="Doctor Consultation"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/20"></div>
      </div>
    </div>
  );
};

export default SignIn;
