Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB9BF70000 ntdll.dll
7FFB9B450000 KERNEL32.DLL
7FFB99630000 KERNELBASE.dll
7FFB9BCF0000 USER32.dll
7FFB99060000 win32u.dll
7FFB9BBF0000 GDI32.dll
7FFB99500000 gdi32full.dll
7FFB991B0000 msvcp_win.dll
7FFB99090000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB9BC30000 advapi32.dll
7FFB9B520000 msvcrt.dll
7FFB99E70000 sechost.dll
7FFB99A10000 bcrypt.dll
7FFB99D50000 RPCRT4.dll
7FFB98850000 CRYPTBASE.DLL
7FFB99480000 bcryptPrimitives.dll
7FFB9B410000 IMM32.DLL
