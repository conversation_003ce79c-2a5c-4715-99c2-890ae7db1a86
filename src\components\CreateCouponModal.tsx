import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  PRODUCT_CATEGORIES,
  PRODUCTS_LIST,
  isValidCategory,
  isValidProduct,
  getCategoryForProduct,
} from "@/constants/products";

interface CreateCouponModalProps {
  isOpen: boolean;
  onClose: () => void;
  coupon?: any;
  onSave: (couponData: any) => void;
}

const CreateCouponModal = ({
  isOpen,
  onClose,
  coupon,
  onSave,
}: CreateCouponModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    code: "",
    description: "",
    productName: "",
    category: "",
    type: "Percentage",
    value: "",
    minOrder: "",
    maxDiscount: "",
    usageLimit: "",
    validFrom: "",
    validTo: "",
    status: "Active",
  });

  useEffect(() => {
    if (coupon) {
      setFormData({
        code: coupon.code || "",
        description: coupon.description || "",
        productName: coupon.productName || "",
        category: coupon.category || "",
        type: coupon.type || "Percentage",
        value: coupon.value?.toString() || "",
        minOrder: coupon.minOrder?.toString() || "",
        maxDiscount: coupon.maxDiscount?.toString() || "",
        usageLimit: coupon.usageLimit?.toString() || "",
        validFrom: coupon.validFrom || "",
        validTo: coupon.validTo || "",
        status: coupon.status || "Active",
      });
    } else {
      setFormData({
        code: "",
        description: "",
        productName: "",
        category: "",
        type: "Percentage",
        value: "",
        minOrder: "",
        maxDiscount: "",
        usageLimit: "",
        validFrom: "",
        validTo: "",
        status: "Active",
      });
    }
  }, [coupon]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !formData.code ||
      !formData.productName ||
      !formData.type ||
      !formData.value ||
      !formData.usageLimit ||
      !formData.validFrom ||
      !formData.validTo
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Validate product name exists in our product list
    if (!isValidProduct(formData.productName)) {
      toast({
        title: "Invalid Product",
        description: "Please select a valid product from the list",
        variant: "destructive",
      });
      return;
    }

    // Validate category if provided
    if (formData.category && !isValidCategory(formData.category)) {
      toast({
        title: "Invalid Category",
        description: "Please select a valid category",
        variant: "destructive",
      });
      return;
    }

    // Auto-set category based on product if not provided
    const productCategory = getCategoryForProduct(formData.productName);
    const finalCategory = formData.category || productCategory || "";

    const couponData = {
      ...formData,
      category: finalCategory,
      value: parseFloat(formData.value),
      minOrder: parseFloat(formData.minOrder),
      maxDiscount: parseFloat(formData.maxDiscount),
      usageLimit: parseInt(formData.usageLimit),
      id: coupon?.id || Date.now(),
    };

    onSave(couponData);

    toast({
      title: "Success",
      description: coupon
        ? "Coupon updated successfully"
        : "Coupon created successfully",
    });

    onClose();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            {coupon ? "Edit Coupon" : "Create New Coupon"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="code"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Coupon Code *
              </Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
                placeholder="Enter coupon code"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="productName"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Product Name *
              </Label>
              <Select
                value={formData.productName}
                onValueChange={(value) => {
                  handleInputChange("productName", value);
                  // Auto-set category when product is selected
                  const productCategory = getCategoryForProduct(value);
                  if (productCategory) {
                    handleInputChange("category", productCategory);
                  }
                }}
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCTS_LIST.map((product) => (
                    <SelectItem key={product.id} value={product.name}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="description"
              className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
            >
              Description
            </Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter coupon description"
              className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="category"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Category
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange("category", value)}
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCT_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="type"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Discount Type *
              </Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleInputChange("type", value)}
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Percentage">Percentage</SelectItem>
                  <SelectItem value="Fixed">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="value"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                {formData.type === "Percentage"
                  ? "Discount Percentage *"
                  : "Discount Amount *"}
              </Label>
              <Input
                id="value"
                type="number"
                value={formData.value}
                onChange={(e) => handleInputChange("value", e.target.value)}
                placeholder={
                  formData.type === "Percentage"
                    ? "Enter percentage"
                    : "Enter amount"
                }
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="minOrder"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Minimum Order Amount
              </Label>
              <Input
                id="minOrder"
                type="number"
                value={formData.minOrder}
                onChange={(e) => handleInputChange("minOrder", e.target.value)}
                placeholder="Enter minimum order amount"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="maxDiscount"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Maximum Discount
              </Label>
              <Input
                id="maxDiscount"
                type="number"
                value={formData.maxDiscount}
                onChange={(e) =>
                  handleInputChange("maxDiscount", e.target.value)
                }
                placeholder="Enter maximum discount"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="usageLimit"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Usage Limit *
              </Label>
              <Input
                id="usageLimit"
                type="number"
                value={formData.usageLimit}
                onChange={(e) =>
                  handleInputChange("usageLimit", e.target.value)
                }
                placeholder="Enter usage limit"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="validFrom"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Valid From
              </Label>
              <Input
                id="validFrom"
                type="date"
                value={formData.validFrom}
                onChange={(e) => handleInputChange("validFrom", e.target.value)}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="validTo"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Valid To *
              </Label>
              <Input
                id="validTo"
                type="date"
                value={formData.validTo}
                onChange={(e) => handleInputChange("validTo", e.target.value)}
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="status"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-6 py-2 text-base transition-colors duration-500 ease-in-out"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-[#FFD700] hover:bg-[#E6C200] text-black text-base px-6 py-2 transition-colors duration-300 ease-in-out"
            >
              {coupon ? "Update Coupon" : "Create Coupon"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateCouponModal;
