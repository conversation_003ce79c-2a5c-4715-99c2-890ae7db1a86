# Dr. Kumar Laboratories Admin Dashboard

A modern, feature-rich admin dashboard for managing Dr. Kumar Laboratories' e-commerce operations, built with Next.js, React, and TypeScript.

## 🚀 Features

### 📊 Dashboard & Analytics
- **Real-time Analytics**: Comprehensive sales, revenue, and performance metrics
- **Interactive Charts**: Dynamic charts with daily, monthly, and yearly views using Recharts
- **Animated Statistics**: Smooth number animations for better user experience
- **Sales Tracking**: Detailed sales breakdown with period-based filtering

### 🛍️ Product Management
- **Product Catalog**: Complete product inventory management
- **Category Management**: Organize products by categories (Health Drinks, Supplements, Ayurvedic)
- **Stock Management**: Real-time stock tracking and alerts
- **Pricing Control**: MRP and selling price management
- **Product Status**: Active/Inactive product status control
- **Bulk Operations**: Export products to Excel, bulk status updates

### 📦 Order Management
- **Order Tracking**: Complete order lifecycle management
- **Status Updates**: Processing, Shipped, Delivered, Cancelled status tracking
- **Customer Details**: Integrated customer information with orders
- **Order History**: Detailed order history and analytics
- **Search & Filter**: Advanced search and filtering capabilities

### 👥 Customer Management
- **Customer Database**: Comprehensive customer information management
- **Customer Analytics**: Order history, spending patterns, and engagement metrics
- **Communication Tools**: Email notification system for customers
- **Customer Status**: Active/Inactive customer management
- **Data Export**: Export customer data to CSV format

### 👨‍💼 Staff Management
- **Team Management**: Complete staff directory and information
- **Department Organization**: Multi-department staff organization
- **Access Control**: Role-based permissions and status management
- **Staff Analytics**: Department-wise staff distribution
- **Add/Edit Staff**: Dynamic staff addition and editing capabilities

### 🏪 Inventory Management
- **Stock Tracking**: Real-time inventory levels
- **Low Stock Alerts**: Automated alerts for low inventory
- **Purchase Management**: Track purchases and suppliers
- **Inventory Analytics**: Stock value and movement tracking

### 🎫 Coupons & Referrals
- **Coupon System**: Create and manage discount coupons
- **Referral Program**: Customer referral tracking and rewards
- **Promotional Tools**: Discount management and analytics

### 📧 Communication & Notifications
- **Email System**: Integrated email sending capabilities
- **Notification Center**: Centralized notification management
- **Customer Communications**: Bulk email and notification tools

### 🔧 Settings & Configuration
- **Profile Management**: User profile and settings
- **Theme Toggle**: Dark/Light mode support
- **General Settings**: System-wide configuration options

## 🛠️ Technology Stack

### Frontend
- **Next.js 14**: React framework with App Router
- **React 18**: Modern React with hooks and functional components
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **Lucide React**: Beautiful icon library

### UI Components
- **Shadcn/ui**: Modern component library
- **Recharts**: Interactive charts and data visualization
- **React Hook Form**: Form handling with validation
- **Zod**: Schema validation
- **Sonner**: Toast notifications
- **React Day Picker**: Date picker component

### State Management & Data
- **TanStack Query**: Server state management
- **React Query**: Data fetching and caching
- **Date-fns**: Date manipulation utilities

### Development Tools
- **ESLint**: Code linting
- **PostCSS**: CSS processing
- **Autoprefixer**: CSS vendor prefixing
- **TypeScript**: Static type checking

## 📁 Project Structure

```
Dr.Kumar-admin/
├── app/                    # Next.js App Router pages
│   ├── components/         # React components
│   │   ├── auth/          # Authentication components
│   │   ├── ui/            # Reusable UI components
│   │   └── settings/      # Settings components
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries
│   ├── pages/             # Additional pages
│   └── utils/             # Utility functions
├── public/                # Static assets
└── components.json        # Shadcn/ui configuration
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Dr.Kumar-admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🎨 UI/UX Features

### Design System
- **Modern Interface**: Clean, professional design
- **Responsive Design**: Mobile-first responsive layout
- **Dark/Light Mode**: Theme switching capability
- **Accessibility**: WCAG compliant components
- **Smooth Animations**: CSS transitions and micro-interactions

### User Experience
- **Intuitive Navigation**: Easy-to-use sidebar navigation
- **Search & Filter**: Advanced search and filtering across all modules
- **Real-time Updates**: Live data updates and notifications
- **Bulk Operations**: Efficient bulk data management
- **Export Functionality**: Data export in multiple formats

## 📊 Key Modules

### Dashboard
- Revenue analytics with interactive charts
- Order statistics and trends
- Top-performing products
- Recent activity feed

### Products
- Product catalog management
- Stock level tracking
- Category organization
- Pricing management

### Orders
- Order lifecycle management
- Customer order history
- Status tracking and updates
- Order analytics

### Customers
- Customer database management
- Communication tools
- Customer analytics
- Export capabilities

### Staff
- Team member management
- Department organization
- Access control
- Staff analytics

## 🔐 Authentication & Security

- **Secure Authentication**: Protected routes and components
- **Role-based Access**: Different permission levels
- **Session Management**: Secure session handling
- **Data Validation**: Input validation and sanitization

## 📱 Responsive Design

The dashboard is fully responsive and optimized for:
- Desktop computers
- Tablets
- Mobile devices
- Different screen resolutions

## 🎯 Performance Features

- **Optimized Loading**: Fast page loads and transitions
- **Lazy Loading**: Components loaded on demand
- **Caching**: Efficient data caching strategies
- **Code Splitting**: Optimized bundle sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- Dashboard analytics and reporting
- Product and inventory management
- Order processing system
- Customer relationship management
- Staff management tools

---

**Built with ❤️ for Dr. Kumar Laboratories**
