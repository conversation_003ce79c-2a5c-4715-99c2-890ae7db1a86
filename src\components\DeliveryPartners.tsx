
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Truck, ChevronDown, ChevronUp } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const partnerStats = [
  { title: "Total Partners", value: "4", color: "text-gray-900" },
  { title: "Active", value: "3", color: "text-green-600" },
  { title: "Inactive", value: "1", color: "text-red-600" },
  { title: "Pending", value: "0", color: "text-orange-600" },
];

const partnersData = [
  {
    id: 1,
    name: "BlueDart Express",
    contactPerson: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+91 9876543210",
    serviceArea: "Pan India",
    deliveryTypes: ["Standard", "Express", "COD"],
    status: "Active",
    performance: "98.5%",
    totalDeliveries: 1250,
    onTimeDeliveries: 1232,
    deliveryDetails: {
      averageTime: "2.1 days",
      successRate: "98.5%",
      coverage: "28 states",
      lastDelivery: "2024-01-20"
    }
  },
  {
    id: 2,
    name: "DTDC Courier",
    contactPerson: "Priya Patel",
    email: "<EMAIL>", 
    phone: "+91 9876543211",
    serviceArea: "Major Cities",
    deliveryTypes: ["Standard", "COD"],
    status: "Active",
    performance: "95.2%",
    totalDeliveries: 856,
    onTimeDeliveries: 815,
    deliveryDetails: {
      averageTime: "3.2 days",
      successRate: "95.2%",
      coverage: "15 cities",
      lastDelivery: "2024-01-19"
    }
  },
  {
    id: 3,
    name: "Delhivery",
    contactPerson: "Rahul Singh",
    email: "<EMAIL>",
    phone: "+91 9876543212", 
    serviceArea: "Metro & Tier-1",
    deliveryTypes: ["Standard", "Express", "COD", "Same Day"],
    status: "Active",
    performance: "97.8%",
    totalDeliveries: 2145,
    onTimeDeliveries: 2098,
    deliveryDetails: {
      averageTime: "1.8 days",
      successRate: "97.8%",
      coverage: "22 cities",
      lastDelivery: "2024-01-20"
    }
  },
  {
    id: 4,
    name: "Local Express",
    contactPerson: "Sneha Gupta",
    email: "<EMAIL>",
    phone: "+91 9876543213",
    serviceArea: "Local Area Only",
    deliveryTypes: ["Standard", "Same Day"],
    status: "Inactive",
    performance: "89.3%",
    totalDeliveries: 234,
    onTimeDeliveries: 209,
    deliveryDetails: {
      averageTime: "0.8 days",
      successRate: "89.3%",
      coverage: "3 cities",
      lastDelivery: "2024-01-15"
    }
  }
];

const DeliveryPartners = () => {
  const { toast } = useToast();
  const [partners, setPartners] = useState(partnersData);
  const [expandedRows, setExpandedRows] = useState<number[]>([]);

  const handleStatusChange = (id: number, newStatus: string) => {
    setPartners(prev => prev.map(partner => 
      partner.id === id ? { ...partner, status: newStatus } : partner
    ));
    
    toast({
      title: "Status Updated",
      description: `Delivery partner status changed to ${newStatus}`,
    });
  };

  const toggleRowExpansion = (id: number) => {
    setExpandedRows(prev => 
      prev.includes(id) 
        ? prev.filter(rowId => rowId !== id)
        : [...prev, id]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-700";
      case "Inactive": return "bg-red-100 text-red-700";
      case "Pending": return "bg-orange-100 text-orange-700";
      case "Failed": return "bg-gray-100 text-gray-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getPerformanceColor = (performance: string) => {
    const percent = parseFloat(performance);
    if (percent >= 95) return "text-green-600";
    if (percent >= 90) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Delivery Partners</h1>
          <p className="text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">Manage delivery partners and their performance</p>
        </div>
      </div>

      {/* Partner Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {partnerStats.map((stat, index) => (
          <Card key={index} className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">{stat.title}</p>
                <AnimatedNumber value={stat.value} className="text-2xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Partners Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Delivery Partners</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Partner Details</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Contact Person</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Service Area</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Delivery Types</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Performance</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Status</TableHead>
                <TableHead className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {partners.map((partner) => (
                <>
                  <TableRow key={partner.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <AnimatedText className="font-medium">{partner.name}</AnimatedText>
                        <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">{partner.email}</span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">{partner.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">{partner.contactPerson}</span>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {partner.serviceArea}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {partner.deliveryTypes.map((type, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className={`font-medium ${getPerformanceColor(partner.performance)}`}>
                          {partner.performance}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                          {partner.onTimeDeliveries}/{partner.totalDeliveries} on time
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select 
                        value={partner.status} 
                        onValueChange={(value) => handleStatusChange(partner.id, value)}
                      >
                        <SelectTrigger className="w-28">
                          <SelectValue>
                            <Badge 
                              variant="secondary"
                              className={getStatusColor(partner.status)}
                            >
                              {partner.status}
                            </Badge>
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent> 
                          <SelectItem value="Active">Active</SelectItem>
                          <SelectItem value="Inactive">Inactive</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="Failed">Failed</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleRowExpansion(partner.id)}
                        className="flex items-center gap-2 transition-colors duration-300 ease-in-out"
                      >
                        <Truck className="w-4 h-4" />
                        Delivery
                        {expandedRows.includes(partner.id) ? 
                          <ChevronUp className="w-4 h-4" /> : 
                          <ChevronDown className="w-4 h-4" />
                        }
                      </Button>
                    </TableCell>
                  </TableRow>
                  {expandedRows.includes(partner.id) && (
                    <TableRow>
                      <TableCell colSpan={7} className="bg-gray-50 dark:bg-gray-700 transition-colors duration-500 ease-in-out">
                        <div className="p-4 space-y-3">
                          <h4 className="font-semibold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">Delivery Details</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">Average Time</span>
                              <p className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">{partner.deliveryDetails.averageTime}</p>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">Success Rate</span>
                              <p className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">{partner.deliveryDetails.successRate}</p>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">Coverage</span>
                              <p className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">{partner.deliveryDetails.coverage}</p>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">Last Delivery</span>
                              <p className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">{partner.deliveryDetails.lastDelivery}</p>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeliveryPartners;
