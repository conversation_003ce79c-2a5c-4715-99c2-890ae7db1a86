import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Camera } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImageCropModal from "./ImageCropModal";
import { useProfile } from "@/contexts/ProfileContext";

const ProfilePage = () => {
  const { toast } = useToast();
  const { profileData, updateProfileData, updateProfileImage, getInitials } =
    useProfile();
  const [isCropModalOpen, setIsCropModalOpen] = useState(false);

  const handleImageUpload = () => {
    setIsCropModalOpen(true);
  };

  const handleCropComplete = (croppedImageUrl: string) => {
    updateProfileImage(croppedImageUrl);
  };

  const handleUpdateProfile = () => {
    toast({
      title: "Profile Updated",
      description: "Your profile has been successfully updated.",
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
          Profile
        </h1>
        <p className="text-base text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
          Manage your profile information
        </p>
      </div>

      {/* Profile Section */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-8">
          <div className="flex items-center gap-8 mb-8">
            <div className="relative">
              {profileData.profileImage ? (
                <button
                  onClick={handleImageUpload}
                  className="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-300 ease-in-out hover:scale-105 cursor-pointer group"
                  title="Click to change profile photo"
                >
                  <img
                    src={profileData.profileImage}
                    alt="Profile"
                    className="w-full h-full object-cover object-center group-hover:opacity-80 transition-opacity duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-300">
                    <Camera className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </button>
              ) : (
                <button
                  onClick={handleImageUpload}
                  className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center border-4 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-300 ease-in-out hover:scale-105 cursor-pointer group"
                  title="Click to upload profile photo"
                >
                  <Camera className="w-12 h-12 text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300" />
                </button>
              )}
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                {profileData.name}
              </h2>
              <p className="text-xl text-gray-600 dark:text-white">
                {profileData.jobTitle}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label
                htmlFor="name"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Name
              </Label>
              <Input
                id="name"
                value={profileData.name}
                className="transition-colors duration-500 ease-in-out bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-base"
                readOnly
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={profileData.email}
                className="transition-colors duration-500 ease-in-out bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-base"
                readOnly
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="phone"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Phone Number
              </Label>
              <Input
                id="phone"
                value={profileData.phone}
                className="transition-colors duration-500 ease-in-out bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-base"
                readOnly
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="jobTitle"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Job Title
              </Label>
              <Input
                id="jobTitle"
                value={profileData.jobTitle}
                className="transition-colors duration-500 ease-in-out bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-base"
                readOnly
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label
                htmlFor="department"
                className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300"
              >
                Department
              </Label>
              <Input
                id="department"
                value={profileData.department}
                className="transition-colors duration-500 ease-in-out bg-gray-50 dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-base"
                readOnly
              />
            </div>
          </div>

          {/* Read-only notice - only show for non-Super Admin users */}
          {profileData.jobTitle !== "Super Admin" && (
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-base text-blue-800 dark:text-blue-200">
                Profile information is read-only. Contact your administrator to
                make changes.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <ImageCropModal
        isOpen={isCropModalOpen}
        onClose={() => setIsCropModalOpen(false)}
        onCropComplete={handleCropComplete}
      />
    </div>
  );
};

export default ProfilePage;
