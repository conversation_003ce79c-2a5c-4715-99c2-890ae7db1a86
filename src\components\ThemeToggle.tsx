import React from "react";
import { Moon, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/components/theme-provider";

const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);

    if (newTheme === "dark") {
      toast({
        title: "🌙 Dark Mode Activated",
        description: "Interface switched to dark theme",
        duration: 2000,
      });
    } else {
      toast({
        title: "☀️ Light Mode Activated",
        description: "Interface switched to light theme",
        duration: 2000,
      });
    }
  };

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleTheme}
      className="fixed bottom-4 right-4 w-12 h-12 rounded-full shadow-lg z-50 bg-white dark:bg-gray-800 border-2 hover:scale-110 transition-all duration-300 ease-in-out hover:shadow-xl"
      title={theme === "dark" ? "Switch to light mode" : "Switch to dark mode"}
      aria-label={
        theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
      }
      role="switch"
      aria-checked={theme === "dark"}
    >
      <div className="relative">
        {theme === "dark" ? (
          <Sun className="h-5 w-5 text-yellow-500 animate-in spin-in-180 duration-500" />
        ) : (
          <Moon className="h-5 w-5 text-gray-700 animate-in spin-in-180 duration-500" />
        )}
      </div>
    </Button>
  );
};

export default ThemeToggle;
