import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye } from "lucide-react";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

const returnStats = [
  { title: "Total Returns", value: "4", color: "text-gray-900" },
  { title: "Pending", value: "2", color: "text-orange-600" },
  { title: "Approved", value: "1", color: "text-green-600" },
  { title: "Refunded", value: "1", color: "text-blue-600" },
];

const returnsData = [
  {
    id: 1,
    orderNumber: "ORD-2024-001",
    customer: "Rajesh Kumar",
    email: "<EMAIL>",
    product: "Kapiva Aloe Vera Juice",
    quantity: 2,
    amount: "₹498",
    reason: "Defective product",
    status: "Pending",
    requestDate: "2024-01-20",
  },
  {
    id: 2,
    orderNumber: "ORD-2024-002",
    customer: "Priya Sharma",
    email: "<EMAIL>",
    product: "Ashwagandha Capsules",
    quantity: 1,
    amount: "₹599",
    reason: "Wrong item received",
    status: "Approved",
    requestDate: "2024-01-19",
  },
  {
    id: 3,
    orderNumber: "ORD-2024-003",
    customer: "Amit Patel",
    email: "<EMAIL>",
    product: "Triphala Churna",
    quantity: 1,
    amount: "₹149",
    reason: "Not satisfied with quality",
    status: "Refunded",
    requestDate: "2024-01-18",
  },
  {
    id: 4,
    orderNumber: "ORD-2024-004",
    customer: "Sneha Reddy",
    email: "<EMAIL>",
    product: "Giloy Juice",
    quantity: 1,
    amount: "₹349",
    reason: "Damaged packaging",
    status: "Pending",
    requestDate: "2024-01-17",
  },
];

const Returns = () => {
  const { toast } = useToast();
  const [returns, setReturns] = useState(returnsData);
  const [selectedReturn, setSelectedReturn] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Filter returns based on search and status
  const filteredReturns = returns.filter((item) => {
    const matchesSearch =
      item.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      item.status.toLowerCase() === statusFilter.toLowerCase();

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedReturns,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredReturns,
    itemsPerPage: 10,
  });

  const handleStatusChange = (id: number, newStatus: string) => {
    setReturns((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: newStatus } : item
      )
    );

    toast({
      title: "Status Updated",
      description: `Return status changed to ${newStatus}`,
    });
  };

  const handleStatusToggle = (id: number, currentStatus: string) => {
    const statusCycle = ["Pending", "Approved", "Refunded"];
    const currentIndex = statusCycle.indexOf(currentStatus);
    const nextStatus = statusCycle[(currentIndex + 1) % statusCycle.length];
    handleStatusChange(id, nextStatus);
  };

  const handleViewReturn = (returnItem) => {
    setSelectedReturn(returnItem);
    setIsViewModalOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "Approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "In Transit":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "Rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "Refunded":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Returns Management
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Handle product returns and refund requests
          </p>
        </div>
      </div>

      {/* Return Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {returnStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Returns
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Returns
                </label>
                <Input
                  placeholder="Search by order number, customer, or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Returns Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Return Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Order & Customer
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Product
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Amount
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Reason
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Status
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Date
                </TableHead>
                <TableHead className="text-lg text-center text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedReturns.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <AnimatedText className="font-medium text-lg">
                        {item.orderNumber}
                      </AnimatedText>
                      <AnimatedText className="text-base font-medium">
                        {item.customer}
                      </AnimatedText>
                      <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.product}
                      </span>
                      <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        Qty: {item.quantity}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                    {item.amount}
                  </TableCell>
                  <TableCell className="text-center">
                    <span className="text-lg text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out">
                      {item.reason}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      onClick={() => handleStatusToggle(item.id, item.status)}
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getStatusColor(item.status)} text-base`}
                      >
                        {item.status}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-center text-lg text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {item.requestDate}
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewReturn(item)}
                      className="transition-colors duration-300 ease-in-out p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
            totalItems={totalItems}
            itemsPerPage={10}
          />
        </CardContent>
      </Card>

      {/* Return View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Return Request Details
            </DialogTitle>
          </DialogHeader>
          {selectedReturn && (
            <div className="space-y-6 p-4">
              {/* Order Information */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Order Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Order Number
                    </label>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {selectedReturn.orderNumber}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Request Date
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.requestDate}
                    </p>
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Customer Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Customer Name
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.customer}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Email Address
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.email}
                    </p>
                  </div>
                </div>
              </div>

              {/* Product Information */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Product Information
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Product Name
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.product}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Quantity
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.quantity}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Amount
                    </label>
                    <p className="text-lg font-semibold text-green-600">
                      {selectedReturn.amount}
                    </p>
                  </div>
                </div>
              </div>

              {/* Return Details */}
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Return Details
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Reason for Return
                    </label>
                    <p className="text-lg text-gray-900 dark:text-white">
                      {selectedReturn.reason}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Current Status
                    </label>
                    <Badge
                      className={`${getStatusColor(
                        selectedReturn.status
                      )} text-base`}
                    >
                      {selectedReturn.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <Button
                  onClick={() =>
                    handleStatusChange(selectedReturn.id, "Approved")
                  }
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  Approve Return
                </Button>
                <Button
                  onClick={() =>
                    handleStatusChange(selectedReturn.id, "Refunded")
                  }
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Process Refund
                </Button>
                <Button
                  onClick={() =>
                    handleStatusChange(selectedReturn.id, "Rejected")
                  }
                  variant="outline"
                  className="flex-1 border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  Reject Return
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Returns;
