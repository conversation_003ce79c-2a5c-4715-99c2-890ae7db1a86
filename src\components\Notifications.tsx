import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Bell,
  AlertCircle,
  FileText,
  BarChart3,
  Calendar,
  CheckCircle,
  Trash2,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const notificationCategories = [
  { title: "All", count: 8, active: true },
  { title: "Alerts", count: 3, active: false },
  { title: "Reports", count: 3, active: false },
  { title: "Updates", count: 2, active: false },
];

const notificationsData = [
  {
    id: 1,
    type: "alert",
    title: "Low Stock Alert",
    message: "Ashwagandha Capsules stock is running low (5 units remaining)",
    time: "2 minutes ago",
    icon: AlertCircle,
    priority: "high",
    read: false,
  },
  {
    id: 2,
    type: "report",
    title: "Daily Sales Report",
    message: "Today's sales report is now available for review",
    time: "1 hour ago",
    icon: Bar<PERSON>hart3,
    priority: "medium",
    read: false,
  },
  {
    id: 3,
    type: "update",
    title: "Profile Updated",
    message: "Your profile information has been successfully updated",
    time: "2 hours ago",
    icon: CheckCircle,
    priority: "low",
    read: true,
  },
  {
    id: 4,
    type: "alert",
    title: "Order Cancelled",
    message: "Order #ORD-2024-001 has been cancelled by customer",
    time: "3 hours ago",
    icon: AlertCircle,
    priority: "medium",
    read: false,
  },
  {
    id: 5,
    type: "report",
    title: "Weekly Performance Report",
    message: "Your weekly performance summary is ready",
    time: "1 day ago",
    icon: FileText,
    priority: "medium",
    read: true,
  },
  {
    id: 6,
    type: "alert",
    title: "System Maintenance",
    message: "Scheduled maintenance tonight from 2 AM to 4 AM",
    time: "1 day ago",
    icon: AlertCircle,
    priority: "high",
    read: false,
  },
  {
    id: 7,
    type: "report",
    title: "Monthly Sales Report",
    message: "January 2024 sales report has been generated",
    time: "2 days ago",
    icon: BarChart3,
    priority: "low",
    read: true,
  },
  {
    id: 8,
    type: "update",
    title: "New Feature Released",
    message: "Inventory batch tracking feature is now available",
    time: "3 days ago",
    icon: Calendar,
    priority: "low",
    read: true,
  },
];

const Notifications = () => {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState(notificationsData);
  const [activeCategory, setActiveCategory] = useState("All");

  // Auto-animate new notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate new notification arrival
      const newNotification = {
        id: Date.now(),
        type: "alert",
        title: "New Order Received",
        message: `Order #ORD-${Date.now()} has been placed`,
        time: "Just now",
        icon: AlertCircle,
        priority: "medium",
        read: false,
      };

      // Randomly add new notifications (for demonstration)
      if (Math.random() > 0.95) {
        setNotifications((prev) => [newNotification, ...prev]);
        toast({
          title: "New Notification",
          description: newNotification.title,
        });
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [toast]);

  const filteredNotifications =
    activeCategory === "All"
      ? notifications
      : notifications.filter(
          (n) =>
            n.type === activeCategory.toLowerCase() ||
            (activeCategory === "Reports" && n.type === "report") ||
            (activeCategory === "Alerts" && n.type === "alert") ||
            (activeCategory === "Updates" && n.type === "update")
        );

  const handleMarkAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: true } : n))
    );
  };

  const handleDeleteNotification = (id: number) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
    toast({
      title: "Notification Deleted",
      description: "Notification has been removed",
    });
  };

  const handleMarkAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
    toast({
      title: "All Marked as Read",
      description: "All notifications have been marked as read",
    });
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600";
      case "medium":
        return "text-yellow-600";
      case "low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "alert":
        return "bg-red-100 text-red-700";
      case "report":
        return "bg-blue-100 text-blue-700";
      case "update":
        return "bg-green-100 text-green-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="w-6 h-6 text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Notifications
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
              Stay updated with important alerts and reports
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="bg-red-100 text-red-700">
            {unreadCount} Unread
          </Badge>
          <Button
            variant="outline"
            onClick={handleMarkAllAsRead}
            className="transition-colors duration-300 ease-in-out"
          >
            Mark All Read
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <Card className="lg:col-span-1 transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {notificationCategories.map((category) => (
                <button
                  key={category.title}
                  onClick={() => setActiveCategory(category.title)}
                  className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors duration-300 ease-in-out ${
                    activeCategory === category.title
                      ? "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700"
                      : "hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <span className="font-medium">{category.title}</span>
                  <Badge variant="secondary" className="text-xs">
                    {category.title === "All"
                      ? notifications.length
                      : filteredNotifications.length}
                  </Badge>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card className="lg:col-span-3 transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              {activeCategory} Notifications
              <span className="text-sm font-normal text-gray-500 dark:text-gray-400 ml-2 transition-colors duration-500 ease-in-out">
                ({filteredNotifications.length})
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start gap-4 p-4 rounded-lg border transition-all duration-500 ease-in-out animate-fade-in ${
                    notification.read
                      ? "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
                      : "bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-700 shadow-sm"
                  }`}
                >
                  <div
                    className={`p-2 rounded-lg transition-colors duration-300 ease-in-out ${getTypeColor(
                      notification.type
                    )}`}
                  >
                    <notification.icon className="w-5 h-5" />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3
                          className={`font-medium transition-colors duration-500 ease-in-out ${
                            notification.read
                              ? "text-gray-700 dark:text-gray-300"
                              : "text-gray-900 dark:text-white"
                          }`}
                        >
                          {notification.title}
                        </h3>
                        <p
                          className={`text-sm mt-1 transition-colors duration-500 ease-in-out ${
                            notification.read
                              ? "text-gray-500 dark:text-gray-400"
                              : "text-gray-600 dark:text-gray-300"
                          }`}
                        >
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-3 mt-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                            {notification.time}
                          </span>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getPriorityColor(
                              notification.priority
                            )}`}
                          >
                            {notification.priority}
                          </Badge>
                          <Badge
                            variant="secondary"
                            className={`text-xs ${getTypeColor(
                              notification.type
                            )}`}
                          >
                            {notification.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {notification.category}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsRead(notification.id)}
                            className="text-blue-600 hover:text-blue-700 transition-colors duration-300 ease-in-out"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleDeleteNotification(notification.id)
                          }
                          className="text-red-600 hover:text-red-700 transition-colors duration-300 ease-in-out"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {filteredNotifications.length === 0 && (
                <div className="text-center py-12">
                  <Bell className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4 transition-colors duration-500 ease-in-out" />
                  <p className="text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    No notifications in this category
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Notifications;
