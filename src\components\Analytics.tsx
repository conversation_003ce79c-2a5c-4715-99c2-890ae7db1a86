import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Tooltip,
} from "recharts";
import { CalendarDays, Download, TrendingUp } from "lucide-react";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";

const salesData = [
  { name: "Jan 01", value: 45000 },
  { name: "Jan 02", value: 52000 },
  { name: "Jan 03", value: 48000 },
  { name: "Jan 04", value: 61000 },
  { name: "Jan 05", value: 55000 },
  { name: "Jan 06", value: 67000 },
  { name: "Jan 07", value: 58000 },
];

const productPerformanceData = [
  { name: "Ashwagandha", value: 30000, sold: 245 },
  { name: "<PERSON><PERSON>", value: 25000, sold: 189 },
  { name: "<PERSON><PERSON>", value: 20000, sold: 156 },
  { name: "<PERSON><PERSON><PERSON>", value: 15000, sold: 134 },
];

const categoryRevenueData = [
  { name: "Supplements", value: 40, color: "#00A36C" },
  { name: "Juices", value: 30, color: "#0088FE" },
  { name: "Powders", value: 20, color: "#FFBB28" },
  { name: "Capsules", value: 10, color: "#FF8042" },
];

const reportTabs = [
  { name: "Sales Report", active: true, key: "sales" },
  { name: "Product Performance", active: false, key: "products" },
  { name: "Category Revenue", active: false, key: "category" },
  { name: "Order Sources", active: false, key: "sources" },
];

const metrics = [
  {
    title: "Total Revenue",
    value: "₹387,000",
    change: "+12.5% from last week",
    trend: "up",
    icon: "💰",
  },
  {
    title: "Total Orders",
    value: "1065",
    change: "+8.2% from last week",
    trend: "up",
    icon: "📦",
  },
  {
    title: "Avg Order Value",
    value: "₹363",
    change: "+3.1% from last week",
    trend: "up",
    icon: "📊",
  },
  {
    title: "Active Customers",
    value: "2,500",
    change: "+15.3% from last week",
    trend: "up",
    icon: "👥",
  },
];

const Analytics = () => {
  const [activeTab, setActiveTab] = useState("sales");
  const [startDate, setStartDate] = useState("2025-01-01");
  const [endDate, setEndDate] = useState("2025-06-20");
  const { toast } = useToast();

  const handleExport = async () => {
    try {
      // Create a simple HTML structure for Word export
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Analytics Report - Dr. Kumar Laboratories</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 40px; page-break-inside: avoid; }
            .chart-placeholder { 
              width: 100%; 
              height: 300px; 
              border: 2px solid #ccc; 
              display: flex; 
              align-items: center; 
              justify-content: center; 
              background-color: #f9f9f9; 
              margin: 20px 0;
            }
            .metrics-grid { display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 30px; }
            .metric-card { 
              border: 1px solid #ddd; 
              padding: 15px; 
              border-radius: 8px; 
              flex: 1; 
              min-width: 200px; 
            }
            h1 { color: #00A36C; }
            h2 { color: #333; border-bottom: 2px solid #00A36C; padding-bottom: 10px; }
            .date-range { text-align: center; margin-bottom: 20px; font-style: italic; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Dr. Kumar Laboratories</h1>
            <h2>Analytics & Performance Report</h2>
            <div class="date-range">Report Period: ${startDate} to ${endDate}</div>
          </div>

          <div class="section">
            <h2>Key Performance Metrics</h2>
            <div class="metrics-grid">
              ${metrics
                .map(
                  (metric) => `
                <div class="metric-card">
                  <h3>${metric.title}</h3>
                  <p style="font-size: 24px; font-weight: bold; color: #00A36C;">${metric.value}</p>
                  <p style="color: green; font-size: 14px;">${metric.change}</p>
                </div>
              `
                )
                .join("")}
            </div>
          </div>

          <div class="section">
            <h2>Sales Report - Daily Trend</h2>
            <div class="chart-placeholder">
              <div style="text-align: center;">
                <h3>Daily Sales Performance</h3>
                <p>Revenue trend from ${salesData[0].name} to ${
        salesData[salesData.length - 1].name
      }</p>
                <p>Peak: ₹${Math.max(
                  ...salesData.map((d) => d.value)
                ).toLocaleString()}</p>
                <p>Average: ₹${Math.round(
                  salesData.reduce((a, b) => a + b.value, 0) / salesData.length
                ).toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div class="section">
            <h2>Product Performance Analysis</h2>
            <div class="chart-placeholder">
              <div style="text-align: center;">
                <h3>Top Performing Products</h3>
                ${productPerformanceData
                  .map(
                    (product) => `
                  <p><strong>${
                    product.name
                  }:</strong> ₹${product.value.toLocaleString()} (${
                      product.sold
                    } units sold)</p>
                `
                  )
                  .join("")}
              </div>
            </div>
          </div>

          <div class="section">
            <h2>Category Revenue Distribution</h2>
            <div class="chart-placeholder">
              <div style="text-align: center;">
                <h3>Revenue by Category</h3>
                ${categoryRevenueData
                  .map(
                    (category) => `
                  <p><strong>${category.name}:</strong> ${category.value}%</p>
                `
                  )
                  .join("")}
              </div>
            </div>
          </div>



          <div class="section">
            <h2>Order Sources Analysis</h2>
            <div class="chart-placeholder">
              <div style="text-align: center;">
                <h3>Order Channel Performance</h3>
                <p><strong>Website:</strong> 845 orders (65%)</p>
                <p><strong>Mobile App:</strong> 320 orders (25%)</p>
                <p><strong>Social Media:</strong> 130 orders (10%)</p>
              </div>
            </div>
          </div>

          <footer style="margin-top: 50px; text-align: center; border-top: 1px solid #ccc; padding-top: 20px;">
            <p>Generated on ${new Date().toLocaleDateString()} | Dr. Kumar Laboratories Admin Dashboard</p>
          </footer>
        </body>
        </html>
      `;

      // Create and download the file
      const blob = new Blob([htmlContent], { type: "application/msword" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Dr-Kumar-Labs-Analytics-Report-${
        new Date().toISOString().split("T")[0]
      }.doc`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: "Analytics report has been downloaded as Word document",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error generating the report",
        variant: "destructive",
      });
    }
  };

  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
    // Update the active state for visual feedback
    reportTabs.forEach((tab) => {
      tab.active = tab.key === tabKey;
    });
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "products":
        return (
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Product Performance - Sales by Product
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={productPerformanceData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="name" stroke="#666" />
                  <YAxis stroke="#666" />
                  <Tooltip
                    formatter={(value, name) => [
                      name === "value" ? `₹${value.toLocaleString()}` : value,
                      name === "value" ? "Revenue" : "Units Sold",
                    ]}
                  />
                  <Bar dataKey="value" fill="#00A36C" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        );
      case "category":
        return (
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Category Revenue Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={categoryRevenueData}
                    cx="50%"
                    cy="50%"
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}
                  >
                    {categoryRevenueData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        );

      case "sources":
        return (
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Order Sources
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { source: "Website", orders: 845, percentage: 65 },
                  { source: "Mobile App", orders: 320, percentage: 25 },
                  { source: "Social Media", orders: 130, percentage: 10 },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out"
                  >
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.source}
                      </h4>
                      <p className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.orders} orders
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.percentage}%
                      </span>
                      <div className="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                        <div
                          className="bg-green-600 dark:bg-green-400 h-2 rounded-full"
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      default:
        return (
          <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
            <CardHeader>
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Sales Report - Daily Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={salesData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="name" stroke="#666" />
                  <YAxis stroke="#666" />
                  <Tooltip
                    formatter={(value) => [
                      `₹${value.toLocaleString()}`,
                      "Revenue",
                    ]}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#00A36C"
                    strokeWidth={3}
                    dot={{ fill: "#00A36C", strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Reports & Analytics
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Business intelligence and performance metrics
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg">
            <CalendarDays className="w-4 h-4 text-gray-500" />
            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="border-0 p-0 h-auto text-sm"
            />
            <span className="text-sm text-gray-500">to</span>
            <Input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="border-0 p-0 h-auto text-sm"
            />
          </div>
          <Button
            className="bg-green-600 hover:bg-green-700 text-white"
            onClick={handleExport}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Report Tabs */}
      <div className="flex gap-2">
        {reportTabs.map((tab, index) => (
          <Button
            key={index}
            variant={activeTab === tab.key ? "default" : "outline"}
            className={activeTab === tab.key ? "bg-gray-900 text-white" : ""}
            onClick={() => handleTabChange(tab.key)}
          >
            {tab.name}
          </Button>
        ))}
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {metric.title}
                </p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {metric.value}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500 dark:text-green-400" />
                  <span className="text-base text-green-600 dark:text-green-400">
                    {metric.change}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Dynamic Tab Content */}
      {renderTabContent()}
    </div>
  );
};

export default Analytics;
