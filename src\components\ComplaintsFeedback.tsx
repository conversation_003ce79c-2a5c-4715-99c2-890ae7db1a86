import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Eye } from "lucide-react";

const feedbackStats = [
  { title: "Total Feedback", value: "5", color: "text-gray-900" },
  { title: "Pending", value: "2", color: "text-orange-600" },
  { title: "In Progress", value: "2", color: "text-blue-600" },
  { title: "Completed", value: "1", color: "text-green-600" },
];

const feedbackData = [
  {
    id: 1,
    customer: "Rajesh Kumar",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Product quality issue",
    message: "The Aloe Vera juice had an unusual taste",
    priority: "High",
    progress: "In Progress",
    date: "2024-01-20",
  },
  {
    id: 2,
    customer: "Priya Sharma",
    email: "<EMAIL>",
    type: "Feedback",
    subject: "Great product experience",
    message: "Very satisfied with the Ashwagandha capsules",
    priority: "Medium",
    progress: "Completed",
    date: "2024-01-19",
  },
  {
    id: 3,
    customer: "Amit Patel",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Delivery delay",
    message: "Order was delivered 3 days late",
    priority: "Medium",
    progress: "Pending",
    date: "2024-01-18",
  },
  {
    id: 4,
    customer: "Sneha Reddy",
    email: "<EMAIL>",
    type: "Suggestion",
    subject: "New product request",
    message: "Please consider adding turmeric supplements",
    priority: "Low",
    progress: "Initiated",
    date: "2024-01-17",
  },
  {
    id: 5,
    customer: "Vikram Singh",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Website navigation issue",
    message:
      "Difficulty finding product categories on the website. The menu structure is confusing and it takes too long to locate specific products.",
    priority: "Medium",
    progress: "Pending",
    date: "2024-01-16",
  },
  {
    id: 6,
    customer: "Anita Gupta",
    email: "<EMAIL>",
    type: "Feedback",
    subject: "Excellent customer service",
    message:
      "The customer support team was very helpful in resolving my query about product usage. Very satisfied with the service quality.",
    priority: "Low",
    progress: "Completed",
    date: "2024-01-15",
  },
  {
    id: 7,
    customer: "Ravi Mehta",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Packaging issue",
    message:
      "The product packaging was damaged during delivery. The bottle was cracked and some content was spilled.",
    priority: "High",
    progress: "In Progress",
    date: "2024-01-14",
  },
  {
    id: 8,
    customer: "Kavita Joshi",
    email: "<EMAIL>",
    type: "Suggestion",
    subject: "Mobile app request",
    message:
      "Please consider developing a mobile app for easier ordering and tracking. It would be very convenient for regular customers.",
    priority: "Medium",
    progress: "Initiated",
    date: "2024-01-13",
  },
  {
    id: 9,
    customer: "Deepak Agarwal",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Product effectiveness",
    message:
      "The immunity booster supplement did not show expected results even after using for 2 months as recommended.",
    priority: "High",
    progress: "Pending",
    date: "2024-01-12",
  },
  {
    id: 10,
    customer: "Sunita Rao",
    email: "<EMAIL>",
    type: "Feedback",
    subject: "Fast delivery",
    message:
      "Very impressed with the quick delivery service. Order was delivered within 24 hours of placing it.",
    priority: "Low",
    progress: "Completed",
    date: "2024-01-11",
  },
  {
    id: 11,
    customer: "Manoj Tiwari",
    email: "<EMAIL>",
    type: "Complaint",
    subject: "Wrong product delivered",
    message:
      "Ordered Ashwagandha capsules but received Brahmi capsules instead. Need immediate replacement.",
    priority: "High",
    progress: "In Progress",
    date: "2024-01-10",
  },
  {
    id: 12,
    customer: "Pooja Sharma",
    email: "<EMAIL>",
    type: "Suggestion",
    subject: "Subscription service",
    message:
      "Would love to have a monthly subscription service for regular products to avoid repeated ordering.",
    priority: "Medium",
    progress: "Initiated",
    date: "2024-01-09",
  },
];

const ComplaintsFeedback = () => {
  const { toast } = useToast();
  const [feedback, setFeedback] = useState(feedbackData);
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const handleProgressChange = (id: number, newProgress: string) => {
    setFeedback((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, progress: newProgress } : item
      )
    );

    toast({
      title: "Status Updated",
      description: `Feedback status changed to ${newProgress}`,
    });
  };

  const handlePriorityChange = (id: number, newPriority: string) => {
    setFeedback((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, priority: newPriority } : item
      )
    );

    toast({
      title: "Priority Updated",
      description: `Priority changed to ${newPriority}`,
    });
  };

  const handlePriorityToggle = (id: number, currentPriority: string) => {
    const priorityCycle = ["High", "Medium", "Low"];
    const currentIndex = priorityCycle.indexOf(currentPriority);
    const nextPriority =
      priorityCycle[(currentIndex + 1) % priorityCycle.length];
    handlePriorityChange(id, nextPriority);
  };

  const handleProgressToggle = (id: number, currentProgress: string) => {
    const progressCycle = ["In Progress", "Completed", "Pending", "Initiated"];
    const currentIndex = progressCycle.indexOf(currentProgress);
    const nextProgress =
      progressCycle[(currentIndex + 1) % progressCycle.length];
    handleProgressChange(id, nextProgress);
  };

  const handleViewFeedback = (feedbackItem: any) => {
    setSelectedFeedback(feedbackItem);
    setIsViewModalOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
  };

  // Filter feedback based on search and status
  const filteredFeedback = feedback.filter((item) => {
    const matchesSearch =
      item.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "pending" && item.progress === "Pending") ||
      (statusFilter === "resolved" &&
        (item.progress === "Completed" || item.progress === "In Progress"));

    return matchesSearch && matchesStatus;
  });

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedFeedback,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredFeedback,
    itemsPerPage: 10,
  });

  const getProgressColor = (progress: string) => {
    switch (progress) {
      case "Initiated":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      case "In Progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "Completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "Pending":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "Medium":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "Low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Feedback
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out">
            Manage customer feedback and resolve complaints
          </p>
        </div>
      </div>

      {/* Feedback Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {feedbackStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className="text-4xl font-bold"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Feedback
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Feedback
                </label>
                <Input
                  placeholder="Search by customer, email, subject, or type..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            Customer Feedback
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Customer
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Type
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Subject
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Priority
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Progress
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Date
                </TableHead>
                <TableHead className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-center">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedFeedback.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <AnimatedText className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.customer}
                      </AnimatedText>
                      <span className="text-base text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                        {item.email}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline" className="text-base">
                      {item.type}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                        {item.subject}
                      </span>
                      <span className="text-base text-gray-500 dark:text-gray-400 max-w-xs truncate transition-colors duration-500 ease-in-out">
                        {item.message}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handlePriorityToggle(item.id, item.priority)
                      }
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getPriorityColor(
                          item.priority
                        )} text-base`}
                      >
                        {item.priority}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handleProgressToggle(item.id, item.progress)
                      }
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getProgressColor(
                          item.progress
                        )} text-base`}
                      >
                        {item.progress}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-center text-lg text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                    {item.date}
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewFeedback(item)}
                      className="transition-colors duration-300 ease-in-out text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
            totalItems={totalItems}
            itemsPerPage={10}
          />
        </CardContent>
      </Card>

      {/* View Feedback Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
              Feedback Details
            </DialogTitle>
          </DialogHeader>

          {selectedFeedback && (
            <div className="space-y-6 p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Customer Information
                  </h3>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Name:</strong> {selectedFeedback.customer}
                  </p>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Email:</strong> {selectedFeedback.email}
                  </p>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Date:</strong> {selectedFeedback.date}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Feedback Status
                  </h3>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Type:</strong> {selectedFeedback.type}
                  </p>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Priority:</strong> {selectedFeedback.priority}
                  </p>
                  <p className="text-base text-gray-700 dark:text-gray-300">
                    <strong>Progress:</strong> {selectedFeedback.progress}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Subject
                </h3>
                <p className="text-base text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  {selectedFeedback.subject}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Message
                </h3>
                <p className="text-base text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg leading-relaxed">
                  {selectedFeedback.message}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ComplaintsFeedback;
