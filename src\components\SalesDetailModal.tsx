
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Bar<PERSON>hart, Bar } from 'recharts';

interface SalesDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  salesData: {
    period: string;
    data: any[];
  } | null;
}

const SalesDetailModal = ({ isOpen, onClose, salesData }: SalesDetailModalProps) => {
  if (!salesData) return null;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="text-gray-900 dark:text-white font-medium">{`${label}`}</p>
          <p className="text-green-600 font-semibold">
            {`Revenue: ₹${payload[0].value.toLocaleString()}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-white">
            {salesData.period.charAt(0).toUpperCase() + salesData.period.slice(1)} Sales Details
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Line Chart */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sales Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesData.data}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="name" className="text-sm fill-gray-600 dark:fill-gray-300" />
                <YAxis className="text-sm fill-gray-600 dark:fill-gray-300" />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#00A36C" 
                  strokeWidth={3}
                  dot={{ fill: '#00A36C', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Bar Chart */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sales Comparison</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={salesData.data}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis dataKey="name" className="text-sm fill-gray-600 dark:fill-gray-300" />
                <YAxis className="text-sm fill-gray-600 dark:fill-gray-300" />
                <Tooltip content={<CustomTooltip />} />
                <Bar dataKey="value" fill="#00A36C" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
              <p className="text-sm text-green-600 dark:text-green-300">Total Revenue</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-200">
                ₹{salesData.data.reduce((sum, item) => sum + item.value, 0).toLocaleString()}
              </p>
            </div>
            <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
              <p className="text-sm text-blue-600 dark:text-blue-300">Average</p>
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-200">
                ₹{Math.round(salesData.data.reduce((sum, item) => sum + item.value, 0) / salesData.data.length).toLocaleString()}
              </p>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
              <p className="text-sm text-purple-600 dark:text-purple-300">Highest</p>
              <p className="text-2xl font-bold text-purple-700 dark:text-purple-200">
                ₹{Math.max(...salesData.data.map(item => item.value)).toLocaleString()}
              </p>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900 p-4 rounded-lg">
              <p className="text-sm text-orange-600 dark:text-orange-300">Lowest</p>
              <p className="text-2xl font-bold text-orange-700 dark:text-orange-200">
                ₹{Math.min(...salesData.data.map(item => item.value)).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SalesDetailModal;
