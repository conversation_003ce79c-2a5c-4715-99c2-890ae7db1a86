
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface EditPartnerModalProps {
  isOpen: boolean;
  onClose: () => void;
  partner: any;
  onUpdate: (updatedPartner: any) => void;
}

const EditPartnerModal = ({ isOpen, onClose, partner, onUpdate }: EditPartnerModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    zone: "",
    availability: false
  });

  useEffect(() => {
    if (partner) {
      setFormData({
        name: partner.name || "",
        phone: partner.phoneNumber || "",
        email: partner.email || "",
        zone: partner.zone || "",
        availability: partner.availability === "true"
      });
    }
  }, [partner]);

  const handleUpdate = () => {
    if (!formData.name || !formData.phone || !formData.email || !formData.zone) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    const updatedPartner = {
      ...partner,
      name: formData.name,
      phoneNumber: formData.phone,
      email: formData.email,
      zone: formData.zone,
      availability: formData.availability ? "true" : "false",
      location: `${formData.zone} Zone`
    };

    onUpdate(updatedPartner);
    toast({
      title: "Success",
      description: "Partner updated successfully",
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Delivery Partner</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="edit-name">Full Name *</Label>
            <Input 
              id="edit-name" 
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-phone">Phone Number *</Label>
            <Input 
              id="edit-phone" 
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-email">Email *</Label>
            <Input 
              id="edit-email" 
              type="email" 
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-zone">Delivery Zone *</Label>
            <Select value={formData.zone} onValueChange={(value) => setFormData({...formData, zone: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select delivery zone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="north">North Zone</SelectItem>
                <SelectItem value="south">South Zone</SelectItem>
                <SelectItem value="east">East Zone</SelectItem>
                <SelectItem value="west">West Zone</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Availability</Label>
            <div className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                id="edit-availability" 
                className="rounded" 
                checked={formData.availability}
                onChange={(e) => setFormData({...formData, availability: e.target.checked})}
              />
              <Label htmlFor="edit-availability" className="text-sm">Available for duties</Label>
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button 
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              onClick={handleUpdate}
            >
              Update Partner
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditPartnerModal;
