Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBAAAF0000 ntdll.dll
7FFBAA6A0000 KERNEL32.DLL
7FFBA82E0000 KERNELBASE.dll
7FFBA8780000 USER32.dll
7FFBA82B0000 win32u.dll
7FFBAA670000 GDI32.dll
7FFBA7FC0000 gdi32full.dll
7FFBA7F20000 msvcp_win.dll
7FFBA7C90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBAA180000 advapi32.dll
7FFBA9260000 msvcrt.dll
7FFBA97A0000 sechost.dll
7FFBA7C60000 bcrypt.dll
7FFBAA550000 RPCRT4.dll
7FFBA73F0000 CRYPTBASE.DLL
7FFBA7BE0000 bcryptPrimitives.dll
7FFBA9DA0000 IMM32.DLL
