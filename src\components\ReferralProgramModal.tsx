
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Gift } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { formatDate } from "@/utils/apiUtils";

interface ReferralProgramModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ReferralProgramModal = ({ isOpen, onClose }: ReferralProgramModalProps) => {
  const [formData, setFormData] = useState({
    programName: "",
    referralBonus: "",
    referredUserReward: "",
    startDate: new Date(),
    endDate: new Date(),
    eligibleProducts: "",
    maxRedemptions: "",
    allowMultipleReferrals: true,
    termsAndConditions: "",
    status: true
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!formData.programName || !formData.referralBonus || !formData.referredUserReward) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      console.log('Setting up referral program:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: "Referral Program Created",
        description: `"${formData.programName}" has been set up successfully`,
      });
      
      // Reset form
      setFormData({
        programName: "",
        referralBonus: "",
        referredUserReward: "",
        startDate: new Date(),
        endDate: new Date(),
        eligibleProducts: "",
        maxRedemptions: "",
        allowMultipleReferrals: true,
        termsAndConditions: "",
        status: true
      });
      
      onClose();
      
    } catch (error) {
      toast({
        title: "Setup Failed",
        description: "Failed to create referral program. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="w-5 h-5" />
            Set Up Referral Program
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Program Name */}
          <div className="space-y-2">
            <Label htmlFor="program-name">Program Name *</Label>
            <Input
              id="program-name"
              value={formData.programName}
              onChange={(e) => setFormData(prev => ({ ...prev, programName: e.target.value }))}
              placeholder="e.g., Friend Referral Program"
            />
          </div>

          {/* Rewards */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="referral-bonus">Referral Bonus (₹) *</Label>
              <Input
                id="referral-bonus"
                type="number"
                value={formData.referralBonus}
                onChange={(e) => setFormData(prev => ({ ...prev, referralBonus: e.target.value }))}
                placeholder="e.g., 100"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="referred-user-reward">Referred User Reward (₹) *</Label>
              <Input
                id="referred-user-reward"
                type="number"
                value={formData.referredUserReward}
                onChange={(e) => setFormData(prev => ({ ...prev, referredUserReward: e.target.value }))}
                placeholder="e.g., 50"
              />
            </div>
          </div>

          {/* Start and End Date */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formatDate(formData.startDate)}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.startDate}
                    onSelect={(date) => date && setFormData(prev => ({ ...prev, startDate: date }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label>End Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formatDate(formData.endDate)}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={formData.endDate}
                    onSelect={(date) => date && setFormData(prev => ({ ...prev, endDate: date }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Eligible Products */}
          <div className="space-y-2">
            <Label htmlFor="eligible-products">Eligible Products (Multi-select)</Label>
            <Input
              id="eligible-products"
              value={formData.eligibleProducts}
              onChange={(e) => setFormData(prev => ({ ...prev, eligibleProducts: e.target.value }))}
              placeholder="All products or specific product categories"
            />
          </div>

          {/* Max Redemptions */}
          <div className="space-y-2">
            <Label htmlFor="max-redemptions">Max Redemptions per User</Label>
            <Input
              id="max-redemptions"
              type="number"
              value={formData.maxRedemptions}
              onChange={(e) => setFormData(prev => ({ ...prev, maxRedemptions: e.target.value }))}
              placeholder="e.g., 5 (leave blank for unlimited)"
            />
          </div>

          {/* Allow Multiple Referrals */}
          <div className="flex items-center space-x-2">
            <Switch
              id="multiple-referrals"
              checked={formData.allowMultipleReferrals}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, allowMultipleReferrals: checked }))}
            />
            <Label htmlFor="multiple-referrals">Allow Multiple Referrals</Label>
          </div>

          {/* Terms & Conditions */}
          <div className="space-y-2">
            <Label htmlFor="terms">Terms & Conditions</Label>
            <Textarea
              id="terms"
              value={formData.termsAndConditions}
              onChange={(e) => setFormData(prev => ({ ...prev, termsAndConditions: e.target.value }))}
              placeholder="Enter program terms and conditions"
              rows={4}
            />
          </div>

          {/* Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="status"
              checked={formData.status}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, status: checked }))}
            />
            <Label htmlFor="status">Active Status</Label>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Setting Up..." : "Create Program"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReferralProgramModal;
