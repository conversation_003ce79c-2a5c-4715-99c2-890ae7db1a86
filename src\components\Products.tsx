import { useState, useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Download, RefreshCw, Edit, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import ProductModal from "./ProductModal";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

// TODO: Replace static product data with API calls to backend
// TODO: Implement CRUD operations for products (Create, Read, Update, Delete)
// TODO: Add API integration for product image uploads and management
const productsData = [
  {
    id: 1,
    name: "Power Stride Juice",
    sku: "KAP-ALO-001",
    category: "Health Drinks",
    description: "Pure Aloe Vera juice for digestive health",
    mrp: 299,
    sellingPrice: 249,
    stock: 150,
    status: "Active",
    image: "/api/placeholder/60/60",
    updatedAt: "2024-01-20",
  },
  {
    id: 2,
    name: "Ashwagandha Capsules",
    sku: "KAP-ASH-001",
    category: "Supplements",
    description: "Premium Ashwagandha for stress relief",
    mrp: 699,
    sellingPrice: 599,
    stock: 75,
    status: "Active",
    image: "/api/placeholder/60/60",
    updatedAt: "2024-01-19",
  },
  {
    id: 3,
    name: "Triphala Churna",
    sku: "KAP-TRI-001",
    category: "Ayurvedic",
    description: "Traditional Triphala powder",
    mrp: 199,
    sellingPrice: 149,
    stock: 0,
    status: "Inactive",
    image: "/api/placeholder/60/60",
    updatedAt: "2024-01-18",
  },
  {
    id: 4,
    name: "Giloy Juice",
    sku: "KAP-GIL-001",
    category: "Health Drinks",
    description: "Immunity booster Giloy juice",
    mrp: 399,
    sellingPrice: 349,
    stock: 89,
    status: "Active",
    image: "/api/placeholder/60/60",
    updatedAt: "2024-01-17",
  },
];

const Products = () => {
  const { toast } = useToast();
  const [products, setProducts] = useState(productsData);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Get unique categories for filter
  const categories = useMemo(() => {
    const uniqueCategories = [...new Set(products.map((p) => p.category))];
    return uniqueCategories;
  }, [products]);

  // Filter products based on search and filters
  const filteredProducts = useMemo(() => {
    return products.filter((product) => {
      const matchesSearch =
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" ||
        product.status.toLowerCase() === statusFilter.toLowerCase();
      const matchesCategory =
        categoryFilter === "all" || product.category === categoryFilter;

      return matchesSearch && matchesStatus && matchesCategory;
    });
  }, [products, searchTerm, statusFilter, categoryFilter]);

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedProducts,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredProducts,
    itemsPerPage: 10,
  });

  // Calculate dynamic stats based on filtered products
  const productStats = useMemo(() => {
    const totalProducts = products.length;
    const activeProducts = products.filter((p) => p.status === "Active").length;
    const inactiveProducts = products.filter(
      (p) => p.status === "Inactive"
    ).length;
    const totalValue = products.reduce(
      (sum, p) => sum + p.sellingPrice * p.stock,
      0
    );

    return [
      {
        title: "Total Products",
        value: totalProducts.toString(),
        color: "text-gray-900",
      },
      {
        title: "Active",
        value: activeProducts.toString(),
        color: "text-green-600",
      },
      {
        title: "Inactive",
        value: inactiveProducts.toString(),
        color: "text-red-600",
      },
      {
        title: "Total Value",
        value: `₹${totalValue.toLocaleString()}`,
        color: "text-blue-600",
      },
    ];
  }, [products]);

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsProductModalOpen(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setIsProductModalOpen(true);
  };

  const handleDeleteProduct = (productId) => {
    setProducts((prev) => prev.filter((p) => p.id !== productId));
    toast({
      title: "Product Deleted",
      description: "Product has been successfully deleted",
    });
  };

  const handleStatusChange = (productId, newStatus) => {
    setProducts((prev) =>
      prev.map((p) => (p.id === productId ? { ...p, status: newStatus } : p))
    );
    toast({
      title: "Status Updated",
      description: "Product status has been successfully updated",
    });
  };

  const handleStatusToggle = (productId, currentStatus) => {
    const nextStatus = currentStatus === "Active" ? "Inactive" : "Active";
    handleStatusChange(productId, nextStatus);
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "Inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "Draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const handleSaveProduct = (productData) => {
    if (selectedProduct) {
      // Update existing product
      setProducts((prev) =>
        prev.map((p) =>
          p.id === selectedProduct.id
            ? { ...productData, id: selectedProduct.id }
            : p
        )
      );
      toast({
        title: "Product Updated",
        description: "Product has been successfully updated",
      });
    } else {
      // Add new product
      setProducts((prev) => [...prev, { ...productData, id: Date.now() }]);
      toast({
        title: "Product Added",
        description: "New product has been successfully added",
      });
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Refresh product data (in real app, this would fetch from API)
    setProducts([...productsData]);
    setIsRefreshing(false);

    toast({
      title: "Products Refreshed",
      description: "Product data has been successfully updated",
    });
  };

  const handleExportToExcel = async () => {
    setIsExporting(true);

    try {
      // Simulate Excel generation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create CSV content (which can be opened in Excel)
      const headers = [
        "Product Name",
        "SKU",
        "Category",
        "MRP",
        "Selling Price",
        "Stock",
        "Status",
        "Last Updated",
      ];
      const csvContent = [
        headers.join(","),
        ...filteredProducts.map((product) =>
          [
            `"${product.name}"`,
            product.sku,
            product.category,
            product.mrp,
            product.sellingPrice,
            product.stock,
            product.status,
            product.updatedAt,
          ].join(",")
        ),
      ].join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `products-export-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: "Products have been exported to Excel format successfully",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export products. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setCategoryFilter("all");
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Product Management
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Manage your product catalog and inventory
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-green-600 hover:bg-green-700 text-white text-base px-6 py-3"
            onClick={handleAddProduct}
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Product
          </Button>
          <Button
            variant="outline"
            onClick={handleExportToExcel}
            disabled={isExporting}
            className="text-base px-6 py-3"
          >
            <Download className="w-5 h-5 mr-2" />
            {isExporting ? "Exporting..." : "Export"}
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Product Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {productStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <AnimatedNumber value={stat.value} className="text-3xl" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Products
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Products
                </label>
                <Input
                  placeholder="Search by name or SKU..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <Select
                  value={categoryFilter}
                  onValueChange={setCategoryFilter}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-base text-gray-500 dark:text-gray-400">
              Showing {filteredProducts.length} of {products.length} products
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Image</div>
              <div className="text-center">Product</div>
              <div className="text-center">Category</div>
              <div className="text-center">Price</div>
              <div className="text-center">Stock</div>
              <div className="text-center">Status</div>
              <div className="text-center">Last Updated</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {paginatedProducts.map((product, index) => (
                <div
                  key={index}
                  className="grid grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                >
                  <div className="flex justify-center">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-12 h-12 object-cover rounded-lg border border-gray-200 dark:border-gray-600"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-600">
                        <span className="text-gray-400 text-xs">No Image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <AnimatedText className="font-medium text-lg">
                      {product.name}
                    </AnimatedText>
                    <span className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                      SKU: {product.sku}
                    </span>
                  </div>
                  <div className="text-gray-600 dark:text-gray-300 text-lg text-center">
                    {product.category}
                  </div>
                  <div className="text-center">
                    <div className="flex flex-col items-center">
                      <span className="font-medium text-lg">
                        ₹{product.sellingPrice}
                      </span>
                      <span className="text-base text-gray-500 line-through">
                        ₹{product.mrp}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <span
                      className={`font-medium text-lg ${
                        product.stock > 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {product.stock}
                    </span>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handleStatusToggle(product.id, product.status)
                      }
                      className="w-full max-w-[140px] text-base bg-red-50 hover:bg-red-100 border-red-200 text-red-700 dark:bg-black dark:hover:bg-gray-800 dark:border-gray-600 dark:text-white"
                    >
                      <Badge
                        className={`${getStatusBadgeColor(
                          product.status
                        )} text-base`}
                      >
                        {product.status}
                      </Badge>
                    </Button>
                  </div>
                  <div className="text-center">
                    <span className="text-gray-500 dark:text-gray-400 text-base">
                      {product.updatedAt}
                    </span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditProduct(product)}
                      className="p-2"
                    >
                      <Edit className="w-5 h-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteProduct(product.id)}
                      className="p-2"
                    >
                      <Trash2 className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              totalItems={totalItems}
              itemsPerPage={10}
            />
          </div>
        </CardContent>
      </Card>

      <ProductModal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        product={selectedProduct}
        onSave={handleSaveProduct}
      />
    </div>
  );
};

export default Products;
