import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  MapPin,
  Phone,
  Mail,
  Calendar,
  ShoppingBag,
  Send,
  Plus,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import AddCategoryModal from "./AddCategoryModal";

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  city: string;
  status: string;
  totalOrders: number;
  totalSpent: string;
  lastOrder: string;
  joinDate: string;
}

interface CustomerViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
}

const CustomerViewModal: React.FC<CustomerViewModalProps> = ({
  isOpen,
  onClose,
  customer,
}) => {
  const [showMessageForm, setShowMessageForm] = useState(false);
  const [messageCategory, setMessageCategory] = useState("");
  const [customMessage, setCustomMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [messageCategories, setMessageCategories] = useState([
    { value: "order-update", label: "Order Update" },
    { value: "promotion", label: "Promotion" },
    { value: "support", label: "Customer Support" },
    { value: "feedback", label: "Feedback Request" },
    { value: "general", label: "General" },
  ]);
  const { toast } = useToast();

  if (!customer) return null;

  const handleAddCategory = (newCategory: string) => {
    const categoryValue = newCategory.toLowerCase().replace(/\s+/g, "-");
    const newCategoryObj = { value: categoryValue, label: newCategory };

    if (!messageCategories.find((cat) => cat.value === categoryValue)) {
      setMessageCategories([...messageCategories, newCategoryObj]);
      setMessageCategory(categoryValue);
    }
  };

  const handleSendMessage = async () => {
    if (!messageCategory || !customMessage.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select a category and enter a message",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast({
        title: "Message Sent Successfully",
        description: `Message sent to ${customer.name}`,
      });

      // Reset form
      setMessageCategory("");
      setCustomMessage("");
      setShowMessageForm(false);
    } catch (error) {
      toast({
        title: "Send Failed",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleClose = () => {
    setShowMessageForm(false);
    setMessageCategory("");
    setCustomMessage("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            Customer Details - {customer.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-white">
                  {customer.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {customer.name}
                </h3>
                <Badge
                  variant="secondary"
                  className={
                    customer.status === "Active"
                      ? "bg-green-100 text-green-700"
                      : "bg-red-100 text-red-700"
                  }
                >
                  {customer.status}
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-base">
                  <Mail className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {customer.email}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-base">
                  <Phone className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {customer.phone}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-base">
                  <MapPin className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    {customer.city}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-base">
                  <Calendar className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Joined: {customer.joinDate}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-base">
                  <ShoppingBag className="w-5 h-5 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-300">
                    Last Order: {customer.lastOrder}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Order Statistics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-base font-medium text-gray-600 dark:text-gray-400">
                Total Orders
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customer.totalOrders}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-base font-medium text-gray-600 dark:text-gray-400">
                Total Spent
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {customer.totalSpent}
              </p>
            </div>
          </div>

          <Separator />

          {/* Message Section */}
          {!showMessageForm ? (
            <div className="flex justify-center">
              <Button
                onClick={() => setShowMessageForm(true)}
                className="bg-blue-600 hover:bg-blue-700 text-base px-6 py-3"
              >
                <Send className="w-5 h-5 mr-2" />
                Send Message
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Send Message to {customer.name}
              </h4>

              <div className="space-y-2">
                <Label htmlFor="category">Message Category</Label>
                <Select
                  value={messageCategory}
                  onValueChange={(value) => {
                    if (value === "add_new_category") {
                      setIsAddCategoryModalOpen(true);
                    } else {
                      setMessageCategory(value);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select message category" />
                  </SelectTrigger>
                  <SelectContent>
                    {messageCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                    <SelectItem value="add_new_category">
                      <div className="flex items-center gap-2">
                        <Plus className="w-4 h-4" />
                        Create New Category
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Custom Message</Label>
                <Textarea
                  id="message"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Enter your message here..."
                  rows={4}
                  className="text-base"
                />
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowMessageForm(false)}
                  className="text-base"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSendMessage}
                  disabled={isSending}
                  className="bg-blue-600 hover:bg-blue-700 text-base"
                >
                  {isSending ? "Sending..." : "Send Message"}
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Close Button */}
        {!showMessageForm && (
          <div className="flex justify-end pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              className="text-base"
            >
              Close
            </Button>
          </div>
        )}
      </DialogContent>

      <AddCategoryModal
        isOpen={isAddCategoryModalOpen}
        onClose={() => setIsAddCategoryModalOpen(false)}
        onAdd={handleAddCategory}
      />
    </Dialog>
  );
};

export default CustomerViewModal;
