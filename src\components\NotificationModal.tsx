
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, Send } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface Customer {
  name: string;
  email: string;
  id?: string;
}

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  customers: Customer[];
}

const NotificationModal = ({ isOpen, onClose, customers }: NotificationModalProps) => {
  const [notificationType, setNotificationType] = useState("email");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);

  const handleCustomerToggle = (customerEmail: string, checked: boolean) => {
    setSelectedCustomers(prev => 
      checked 
        ? [...prev, customerEmail]
        : prev.filter(email => email !== customerEmail)
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedCustomers(checked ? customers.map(c => c.email) : []);
  };

  const handleSendNotification = async () => {
    if (!subject.trim() || !message.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in both subject and message",
        variant: "destructive",
      });
      return;
    }

    if (selectedCustomers.length === 0) {
      toast({
        title: "No Recipients",
        description: "Please select at least one customer",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);
    
    try {
      // Simulate Nodemailer integration
      const notificationData = {
        type: notificationType,
        subject,
        message,
        recipients: selectedCustomers,
        timestamp: new Date().toISOString()
      };
      
      console.log('Sending notification via Nodemailer:', notificationData);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Notification Sent",
        description: `${notificationType} sent to ${selectedCustomers.length} customer(s)`,
      });
      
      // Reset form
      setSubject("");
      setMessage("");
      setSelectedCustomers([]);
      onClose();
      
    } catch (error) {
      toast({
        title: "Send Failed",
        description: "Failed to send notification. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Send Notification
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Notification Type */}
          <div>
            <Label htmlFor="type" className="text-sm font-medium">
              Notification Type
            </Label>
            <Select value={notificationType} onValueChange={setNotificationType}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="sms" disabled>SMS (Coming Soon)</SelectItem>
                <SelectItem value="push" disabled>Push Notification (Coming Soon)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div>
            <Label htmlFor="subject" className="text-sm font-medium">
              Subject
            </Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Enter notification subject"
              className="mt-1"
            />
          </div>

          {/* Message */}
          <div>
            <Label htmlFor="message" className="text-sm font-medium">
              Message
            </Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your message here..."
              rows={4}
              className="mt-1"
            />
          </div>

          {/* Recipients */}
          <div>
            <Label className="text-sm font-medium">Select Recipients</Label>
            <div className="mt-2 border rounded-lg p-4 max-h-48 overflow-y-auto">
              <div className="flex items-center space-x-2 mb-3 pb-3 border-b">
                <Checkbox
                  id="select-all"
                  checked={selectedCustomers.length === customers.length}
                  onCheckedChange={(checked) => handleSelectAll(checked === true)}
                />
                <Label htmlFor="select-all" className="font-medium">
                  Select All ({customers.length} customers)
                </Label>
              </div>
              
              <div className="space-y-2">
                {customers.map((customer, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Checkbox
                      id={`customer-${index}`}
                      checked={selectedCustomers.includes(customer.email)}
                      onCheckedChange={(checked) => handleCustomerToggle(customer.email, checked === true)}
                    />
                    <Label htmlFor={`customer-${index}`} className="flex-1">
                      <div className="flex justify-between">
                        <span>{customer.name}</span>
                        <span className="text-gray-500 text-sm">{customer.email}</span>
                      </div>
                    </Label>
                  </div>
                ))}
              </div>
            </div>
            
            {selectedCustomers.length > 0 && (
              <p className="text-sm text-gray-600 mt-2">
                {selectedCustomers.length} recipient(s) selected
              </p>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={isSending}>
              Cancel
            </Button>
            <Button onClick={handleSendNotification} disabled={isSending}>
              {isSending ? (
                <>Sending...</>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Notification
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationModal;
