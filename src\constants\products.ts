// Product and Category constants for validation and consistency across the application

export const PRODUCT_CATEGORIES = [
  "Ayurvedic Medicines",
  "Health Supplements", 
  "Personal Care",
  "Herbal Products",
  "Health Drinks",
  "Supplements",
  "Combo Packs"
] as const;

export type ProductCategory = typeof PRODUCT_CATEGORIES[number];

export const PRODUCTS_LIST = [
  {
    id: 1,
    name: "Kapiva Aloe Vera Juice",
    sku: "KAP-ALO-001",
    category: "Health Drinks" as ProductCategory,
  },
  {
    id: 2,
    name: "Ashwagandha Capsules",
    sku: "KAP-ASH-001", 
    category: "Supplements" as ProductCategory,
  },
  {
    id: 3,
    name: "Triphala Churna",
    sku: "KAP-TRI-001",
    category: "Ayurvedic Medicines" as ProductCategory,
  },
  {
    id: 4,
    name: "Giloy Tablets",
    sku: "KAP-GIL-001",
    category: "Health Supplements" as ProductCategory,
  },
  {
    id: 5,
    name: "<PERSON><PERSON><PERSON>",
    sku: "KAP-NEE-001",
    category: "Personal Care" as ProductCategory,
  },
  {
    id: 6,
    name: "Brahmi Oil",
    sku: "KAP-BRA-001",
    category: "Herbal Products" as ProductCategory,
  },
  {
    id: 7,
    name: "Amla Juice",
    sku: "KAP-AML-001",
    category: "Health Drinks" as ProductCategory,
  },
  {
    id: 8,
    name: "Turmeric Capsules",
    sku: "KAP-TUR-001",
    category: "Health Supplements" as ProductCategory,
  },
  {
    id: 9,
    name: "Ayurvedic Hair Oil",
    sku: "KAP-HAI-001",
    category: "Personal Care" as ProductCategory,
  },
  {
    id: 10,
    name: "Wellness Combo Pack",
    sku: "KAP-WEL-001",
    category: "Combo Packs" as ProductCategory,
  }
] as const;

export type Product = typeof PRODUCTS_LIST[number];

// Helper functions for validation
export const isValidCategory = (category: string): category is ProductCategory => {
  return PRODUCT_CATEGORIES.includes(category as ProductCategory);
};

export const isValidProduct = (productName: string): boolean => {
  return PRODUCTS_LIST.some(product => product.name === productName);
};

export const getProductByName = (productName: string): Product | undefined => {
  return PRODUCTS_LIST.find(product => product.name === productName);
};

export const getProductsByCategory = (category: ProductCategory): Product[] => {
  return PRODUCTS_LIST.filter(product => product.category === category);
};

export const getCategoryForProduct = (productName: string): ProductCategory | undefined => {
  const product = getProductByName(productName);
  return product?.category;
};
