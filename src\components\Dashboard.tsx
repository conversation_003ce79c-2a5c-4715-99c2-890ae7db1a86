import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import { Eye, RefreshCw, CalendarDays, TrendingUp } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";
import SalesDetailModal from "./SalesDetailModal";

const dailyData = [
  { name: "Jan 01", value: 40000 },
  { name: "Jan 02", value: 45000 },
  { name: "Jan 03", value: 42000 },
  { name: "Jan 04", value: 58000 },
  { name: "Jan 05", value: 50000 },
  { name: "Jan 06", value: 62000 },
  { name: "Jan 07", value: 55000 },
];

const monthlyData = [
  { name: "Jan", value: 250000 },
  { name: "Feb", value: 280000 },
  { name: "<PERSON>", value: 260000 },
  { name: "Apr", value: 320000 },
  { name: "May", value: 300000 },
  { name: "Jun", value: 340000 },
];

const yearlyData = [
  { name: "2020", value: 2800000 },
  { name: "2021", value: 3200000 },
  { name: "2022", value: 3800000 },
  { name: "2023", value: 4200000 },
  { name: "2024", value: 4800000 },
];

const recentOrders = [
  {
    orderNumber: "ORD-2024-001",
    customer: "Rajesh Kumar",
    date: "2024-01-22",
    amount: "₹2,350",
    status: "processing",
  },
  {
    orderNumber: "ORD-2024-002",
    customer: "Priya Sharma",
    date: "2024-01-21",
    amount: "₹1,890",
    status: "shipped",
  },
  {
    orderNumber: "ORD-2024-003",
    customer: "Amit Patel",
    date: "2024-01-20",
    amount: "₹3,120",
    status: "delivered",
  },
  {
    orderNumber: "ORD-2024-004",
    customer: "Sneha Reddy",
    date: "2024-01-19",
    amount: "₹950",
    status: "pending",
  },
  {
    orderNumber: "ORD-2024-005",
    customer: "Vikram Singh",
    date: "2024-01-18",
    amount: "₹1,580",
    status: "processing",
  },
];

const topProducts = [
  { name: "Power Stride Juice", sold: 120, revenue: "₹61,250" },
  { name: "Power Stride Tablet", sold: 95, revenue: "₹48,700" },
  { name: "Power Stride Capsules", sold: 80, revenue: "₹40,800" },
  { name: "L-large Sachets", sold: 75, revenue: "₹35,200" },
];

const Dashboard = () => {
  const { toast } = useToast();
  const [chartPeriod, setChartPeriod] = useState("daily");
  const [showAllOrders, setShowAllOrders] = useState(false);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [salesModalOpen, setSalesModalOpen] = useState(false);
  const [selectedSalesData, setSelectedSalesData] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh - in real app, this would fetch fresh data from API
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Reset any view states to show fresh data
    setShowAllOrders(false);
    setShowAllProducts(false);

    setIsRefreshing(false);
    toast({
      title: "Dashboard Refreshed",
      description:
        "Recent orders and top-selling products have been successfully updated",
    });
  };

  const handleViewAllSales = (period: string, data: any[]) => {
    setSelectedSalesData({ period, data });
    setSalesModalOpen(true);
  };

  const toggleOrdersView = () => {
    setShowAllOrders(!showAllOrders);
  };

  const toggleProductsView = () => {
    setShowAllProducts(!showAllProducts);
  };

  const getDisplayData = () => {
    switch (chartPeriod) {
      case "monthly":
        return monthlyData;
      case "yearly":
        return yearlyData;
      default:
        return dailyData;
    }
  };

  const displayData = getDisplayData();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg">
          <p className="text-gray-900 dark:text-white font-medium">{`${label}`}</p>
          <p className="text-green-600 font-semibold">
            {`Value: ₹${payload[0].value.toLocaleString()}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Welcome back! Here's what's happening with your store today.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-base px-6 py-3"
          >
            <RefreshCw
              className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
            />
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  Total Revenue
                </p>
                <AnimatedNumber value="₹2,45,670" className="text-3xl" />
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-sm text-green-600">+12.5%</span>
                  <span className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                💰
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  Total Orders
                </p>
                <AnimatedNumber value="1,234" className="text-3xl" />
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-sm text-green-600">+8.2%</span>
                  <span className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                📦
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  Active Products
                </p>
                <AnimatedNumber value="856" className="text-3xl" />
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-sm text-green-600">+3.1%</span>
                  <span className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                📊
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-base font-medium transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-400">
                  Total Customers
                </p>
                <AnimatedNumber value="3,428" className="text-3xl" />
                <div className="flex items-center gap-1 mt-1">
                  <span className="text-sm text-green-600">+15.3%</span>
                  <span className="text-sm transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                    from last month
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                👥
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Sales Chart */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
              Sales Overview
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant={chartPeriod === "daily" ? "default" : "outline"}
                size="sm"
                onClick={() => setChartPeriod("daily")}
                className="text-base px-4 py-2"
              >
                Daily
              </Button>
              <Button
                variant={chartPeriod === "monthly" ? "default" : "outline"}
                size="sm"
                onClick={() => setChartPeriod("monthly")}
                className="text-base px-4 py-2"
              >
                Monthly
              </Button>
              <Button
                variant={chartPeriod === "yearly" ? "default" : "outline"}
                size="sm"
                onClick={() => setChartPeriod("yearly")}
                className="text-base px-4 py-2"
              >
                Yearly
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={displayData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey="name"
                className="text-sm transition-colors duration-500 ease-in-out fill-gray-600 dark:fill-gray-300"
              />
              <YAxis className="text-sm transition-colors duration-500 ease-in-out fill-gray-600 dark:fill-gray-300" />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#FFD700"
                strokeWidth={3}
                dot={{ fill: "#FFD700", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Recent Orders
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleOrdersView}
                className="text-base px-4 py-2"
              >
                {showAllOrders ? "Show Less" : "View All"}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(showAllOrders ? recentOrders : recentOrders.slice(0, 3)).map(
                (order, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-500 ease-in-out"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                        <span className="text-sm font-medium transition-colors duration-500 ease-in-out text-green-700 dark:text-green-300">
                          {order.customer
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </span>
                      </div>
                      <div>
                        <AnimatedText
                          variant="highlight"
                          className="text-lg font-medium"
                        >
                          {order.orderNumber}
                        </AnimatedText>
                        <p className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                          {order.customer}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-lg transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                        {order.amount}
                      </p>
                      <Badge variant="secondary" className="text-base">
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Selling Products */}
        <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
                Top Selling Products
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleProductsView}
                className="text-base px-4 py-2"
              >
                {showAllProducts ? "Show Less" : "View All"}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {(showAllProducts ? topProducts : topProducts.slice(0, 3)).map(
                (product, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg transition-colors duration-500 ease-in-out"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center transition-colors duration-500 ease-in-out">
                        📦
                      </div>
                      <div>
                        <AnimatedText className="font-medium text-lg">
                          {product.name}
                        </AnimatedText>
                        <p className="text-base transition-colors duration-500 ease-in-out text-gray-500 dark:text-gray-400">
                          {product.sold} sold
                        </p>
                      </div>
                    </div>
                    <AnimatedNumber
                      value={product.revenue}
                      className="font-medium text-lg"
                    />
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <SalesDetailModal
        isOpen={salesModalOpen}
        onClose={() => setSalesModalOpen(false)}
        salesData={selectedSalesData}
      />
    </div>
  );
};

export default Dashboard;
