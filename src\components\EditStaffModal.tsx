import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface EditStaffModalProps {
  isOpen: boolean;
  onClose: () => void;
  staff: any;
  onUpdate: (updatedStaff: any) => void;
}

const EditStaffModal = ({
  isOpen,
  onClose,
  staff,
  onUpdate,
}: EditStaffModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    joinDate: "",
  });

  useEffect(() => {
    if (staff) {
      setFormData({
        name: staff.name || "",
        email: staff.email || "",
        phone: staff.phone || "",
        position: staff.position || "",
        department: staff.department || "",
        joinDate: staff.joinDate || "",
      });
    }
  }, [staff]);

  const handleUpdate = () => {
    if (
      !formData.name ||
      !formData.email ||
      !formData.position ||
      !formData.department
    ) {
      toast({
        title: "Error",
        description: "Please fill all required fields",
        variant: "destructive",
      });
      return;
    }

    const updatedStaff = {
      ...staff,
      name: formData.name,
      email: formData.email,
      phone: formData.phone,
      position: formData.position,
      department: formData.department,
      joinDate: formData.joinDate,
    };

    onUpdate(updatedStaff);
    toast({
      title: "Success",
      description: "Staff member updated successfully",
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Staff Member</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="edit-staff-name">Full Name *</Label>
            <Input
              id="edit-staff-name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-staff-email">Email *</Label>
            <Input
              id="edit-staff-email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-staff-phone">Phone</Label>
            <Input
              id="edit-staff-phone"
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-staff-position">Position *</Label>
            <Select
              value={formData.position}
              onValueChange={(value) =>
                setFormData({ ...formData, position: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select position" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Operations Manager">
                  Operations Manager
                </SelectItem>
                <SelectItem value="Customer Service Lead">
                  Customer Service Lead
                </SelectItem>
                <SelectItem value="Marketing Executive">
                  Marketing Executive
                </SelectItem>
                <SelectItem value="Inventory Manager">
                  Inventory Manager
                </SelectItem>
                <SelectItem value="Quality Analyst">Quality Analyst</SelectItem>
                <SelectItem value="Support Specialist">
                  Support Specialist
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-staff-department">Department *</Label>
            <Select
              value={formData.department}
              onValueChange={(value) =>
                setFormData({ ...formData, department: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Operations">Operations</SelectItem>
                <SelectItem value="Customer Service">
                  Customer Service
                </SelectItem>
                <SelectItem value="Marketing">Marketing</SelectItem>
                <SelectItem value="Inventory">Inventory</SelectItem>
                <SelectItem value="Quality Assurance">
                  Quality Assurance
                </SelectItem>
                <SelectItem value="Support">Support</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-staff-date">Joining Date</Label>
            <Input
              id="edit-staff-date"
              type="date"
              value={formData.joinDate}
              onChange={(e) =>
                setFormData({ ...formData, joinDate: e.target.value })
              }
            />
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
              onClick={handleUpdate}
            >
              Update Staff
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditStaffModal;
