import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Star, StarHalf, Eye } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { usePagination } from "@/hooks/use-pagination";
import { TablePagination } from "@/components/ui/table-pagination";
import AnimatedNumber from "./AnimatedNumber";
import AnimatedText from "./AnimatedText";

// Star Rating Component
const StarRating = ({ rating }: { rating: number }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex items-center gap-1">
      {/* Full Stars */}
      {Array.from({ length: fullStars }).map((_, index) => (
        <Star
          key={`full-${index}`}
          className="w-4 h-4 fill-yellow-400 text-yellow-400"
        />
      ))}
      {/* Half Star */}
      {hasHalfStar && (
        <StarHalf className="w-4 h-4 fill-yellow-400 text-yellow-400" />
      )}
      {/* Empty Stars */}
      {Array.from({ length: emptyStars }).map((_, index) => (
        <Star
          key={`empty-${index}`}
          className="w-4 h-4 text-gray-300 dark:text-gray-600"
        />
      ))}
      <span className="ml-1 text-sm font-medium text-gray-600 dark:text-gray-400">
        {rating.toFixed(1)}
      </span>
    </div>
  );
};

// Sample review data
const reviewsData = [
  {
    id: 1,
    productName: "Skin Renewal Serum",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Sophia Clark",
    rating: 5,
    reviewText: "This serum is amazing! It has transformed my skin...",
    status: "Approved",
    dateSubmitted: "2024-07-26",
  },
  {
    id: 2,
    productName: "Hydrating Face Mask",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Ethan Bennett",
    rating: 4,
    reviewText: "I love this mask, it leaves my skin feeling so soft...",
    status: "Pending",
    dateSubmitted: "2024-07-25",
  },
  {
    id: 3,
    productName: "Anti-Aging Cream",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Olivia Carter",
    rating: 3,
    reviewText: "It's okay, but I didn't see a huge difference...",
    status: "Rejected",
    dateSubmitted: "2024-07-24",
  },
  {
    id: 4,
    productName: "Acne Treatment Gel",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Liam Davis",
    rating: 5,
    reviewText: "This gel cleared up my acne in just a few days!",
    status: "Approved",
    dateSubmitted: "2024-07-23",
  },
  {
    id: 5,
    productName: "Sunscreen SPF 50",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Ava Evans",
    rating: 4,
    reviewText: "Great sunscreen, doesn't leave a white cast...",
    status: "Pending",
    dateSubmitted: "2024-07-22",
  },
  {
    id: 6,
    productName: "Night Repair Serum",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Noah Foster",
    rating: 5,
    reviewText: "Woke up with glowing skin after using this serum!",
    status: "Approved",
    dateSubmitted: "2024-07-21",
  },
  {
    id: 7,
    productName: "Vitamin C Serum",
    productImage: "/api/placeholder/60/60",
    reviewerName: "Isabella Green",
    rating: 4,
    reviewText: "Brightened my skin tone, but a bit sticky...",
    status: "Pending",
    dateSubmitted: "2024-07-20",
  },
];

const ReviewManagement = () => {
  const { toast } = useToast();
  const [reviews, setReviews] = useState(reviewsData);
  const [searchTerm, setSearchTerm] = useState("");
  const [ratingFilter, setRatingFilter] = useState("all");
  const [productFilter, setProductFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedReview, setSelectedReview] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // Get unique products for filter
  const products = useMemo(() => {
    const uniqueProducts = [...new Set(reviews.map((r) => r.productName))];
    return uniqueProducts;
  }, [reviews]);

  // Filter reviews based on search and filters
  const filteredReviews = useMemo(() => {
    return reviews.filter((review) => {
      const matchesSearch =
        review.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.reviewerName.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesRating =
        ratingFilter === "all" || review.rating.toString() === ratingFilter;
      const matchesProduct =
        productFilter === "all" || review.productName === productFilter;
      const matchesStatus =
        statusFilter === "all" ||
        review.status.toLowerCase() === statusFilter.toLowerCase();

      return matchesSearch && matchesRating && matchesProduct && matchesStatus;
    });
  }, [reviews, searchTerm, ratingFilter, productFilter, statusFilter]);

  // Pagination logic
  const {
    currentPage,
    totalPages,
    paginatedData: paginatedReviews,
    goToPage,
    totalItems,
  } = usePagination({
    data: filteredReviews,
    itemsPerPage: 10,
  });

  // Calculate dynamic stats based on reviews
  const reviewStats = useMemo(() => {
    const totalReviews = reviews.length;
    const approvedReviews = reviews.filter(
      (r) => r.status === "Approved"
    ).length;
    const pendingReviews = reviews.filter((r) => r.status === "Pending").length;

    // Calculate overall rating
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const overallRating =
      totalReviews > 0 ? (totalRating / totalReviews).toFixed(1) : "0.0";

    return [
      {
        title: "Total Reviews",
        value: totalReviews.toString(),
        color: "text-gray-900",
      },
      {
        title: "Approved",
        value: approvedReviews.toString(),
        color: "text-green-600",
      },
      {
        title: "Pending",
        value: pendingReviews.toString(),
        color: "text-orange-600",
      },
      {
        title: "Overall Rating",
        value: `${overallRating}★`,
        color: "text-yellow-600",
      },
    ];
  }, [reviews]);

  const handleStatusChange = (reviewId: number, newStatus: string) => {
    setReviews((prev) =>
      prev.map((review) =>
        review.id === reviewId ? { ...review, status: newStatus } : review
      )
    );
    toast({
      title: "Status Updated",
      description: `Review status changed to ${newStatus}`,
    });
  };

  const clearFilters = () => {
    setSearchTerm("");
    setRatingFilter("all");
    setProductFilter("all");
    setStatusFilter("all");
  };

  const handleViewReview = (review) => {
    setSelectedReview(review);
    setIsViewModalOpen(true);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "pending":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getNextStatus = (currentStatus: string) => {
    const statusCycle = ["Pending", "Approved", "Rejected"];
    const currentIndex = statusCycle.indexOf(currentStatus);
    return statusCycle[(currentIndex + 1) % statusCycle.length];
  };

  return (
    <div className="space-y-4 transition-colors duration-500 ease-in-out">
      <div className="flex items-center justify-between pt-2">
        <div>
          <h1 className="text-3xl font-bold transition-colors duration-500 ease-in-out text-gray-900 dark:text-white">
            Review Management
          </h1>
          <p className="text-lg transition-colors duration-500 ease-in-out text-gray-600 dark:text-gray-300">
            Monitor customer feedback and manage reviews for your products.
          </p>
        </div>
      </div>

      {/* Review Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reviewStats.map((stat, index) => (
          <Card
            key={index}
            className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
          >
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {stat.title}
                </p>
                <AnimatedNumber
                  value={stat.value}
                  className={`text-4xl font-bold transition-colors duration-500 ease-in-out ${stat.color} dark:text-white`}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300">
                <span className="font-medium text-xl">
                  🔍 Search & Filter Reviews
                </span>
              </div>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="text-base"
              >
                Clear Filters
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Search Reviews
                </label>
                <Input
                  placeholder="Search by product or reviewer name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base"
                />
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Rating
                </label>
                <Select value={ratingFilter} onValueChange={setRatingFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="5">5 Stars</SelectItem>
                    <SelectItem value="4">4 Stars</SelectItem>
                    <SelectItem value="3">3 Stars</SelectItem>
                    <SelectItem value="2">2 Stars</SelectItem>
                    <SelectItem value="1">1 Star</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Product
                </label>
                <Select value={productFilter} onValueChange={setProductFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Products" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Products</SelectItem>
                    {products.map((product) => (
                      <SelectItem key={product} value={product}>
                        {product}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-base font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews Table */}
      <Card className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="transition-colors duration-500 ease-in-out text-gray-900 dark:text-white text-2xl">
            Reviews ({filteredReviews.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg font-medium transition-colors duration-500 ease-in-out text-gray-700 dark:text-gray-300 text-lg border-b border-gray-200 dark:border-gray-600">
              <div className="text-center">Product Image</div>
              <div className="text-center">Product Name</div>
              <div className="text-center">Reviewer Name</div>
              <div className="text-center">Rating</div>
              <div className="text-center">Review Text</div>
              <div className="text-center">Status</div>
              <div className="text-center">Date Submitted</div>
              <div className="text-center">Actions</div>
            </div>

            {/* Table Rows */}
            <div className="space-y-3">
              {paginatedReviews.map((review, index) => (
                <div
                  key={index}
                  className="grid grid-cols-8 gap-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 items-center transition-colors duration-500 ease-in-out"
                >
                  <div className="flex justify-center">
                    <img
                      src={review.productImage}
                      alt={review.productName}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  </div>
                  <div className="text-center">
                    <AnimatedText className="font-medium text-lg">
                      {review.productName}
                    </AnimatedText>
                  </div>
                  <div className="text-center">
                    <span className="text-gray-600 dark:text-gray-300 text-lg">
                      {review.reviewerName}
                    </span>
                  </div>
                  <div className="flex justify-center">
                    <StarRating rating={review.rating} />
                  </div>
                  <div className="text-center">
                    <span className="text-gray-600 dark:text-gray-300 text-base">
                      {review.reviewText.length > 50
                        ? `${review.reviewText.substring(0, 50)}...`
                        : review.reviewText}
                    </span>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={() =>
                        handleStatusChange(
                          review.id,
                          getNextStatus(review.status)
                        )
                      }
                      className="w-full max-w-[140px] text-base"
                    >
                      <Badge
                        className={`${getStatusBadgeColor(
                          review.status
                        )} text-base`}
                      >
                        {review.status}
                      </Badge>
                    </Button>
                  </div>
                  <div className="text-center">
                    <span className="text-gray-500 dark:text-gray-400 text-base">
                      {review.dateSubmitted}
                    </span>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewReview(review)}
                      className="transition-colors duration-300 ease-in-out p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              totalItems={totalItems}
              itemsPerPage={10}
            />

            {filteredReviews.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400 text-lg">
                  No reviews found matching your criteria.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Review View Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Review Details
            </DialogTitle>
          </DialogHeader>
          {selectedReview && (
            <div className="space-y-6 p-4">
              {/* Product Information */}
              <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <img
                  src={selectedReview.productImage}
                  alt={selectedReview.productName}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedReview.productName}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Product Review
                  </p>
                </div>
              </div>

              {/* Reviewer Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Reviewer Name
                  </label>
                  <p className="text-lg text-gray-900 dark:text-white">
                    {selectedReview.reviewerName}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Date Submitted
                  </label>
                  <p className="text-lg text-gray-900 dark:text-white">
                    {selectedReview.dateSubmitted}
                  </p>
                </div>
              </div>

              {/* Rating */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Rating
                </label>
                <div className="flex items-center gap-2">
                  <StarRating rating={selectedReview.rating} />
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedReview.rating}/5
                  </span>
                </div>
              </div>

              {/* Review Text */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Review Text
                </label>
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <p className="text-gray-900 dark:text-white leading-relaxed">
                    {selectedReview.reviewText}
                  </p>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Current Status
                </label>
                <Badge
                  className={`${getStatusBadgeColor(
                    selectedReview.status
                  )} text-base`}
                >
                  {selectedReview.status}
                </Badge>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <Button
                  onClick={() =>
                    handleStatusChange(selectedReview.id, "Approved")
                  }
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  Approve Review
                </Button>
                <Button
                  onClick={() =>
                    handleStatusChange(selectedReview.id, "Rejected")
                  }
                  variant="outline"
                  className="flex-1 border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  Reject Review
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReviewManagement;
