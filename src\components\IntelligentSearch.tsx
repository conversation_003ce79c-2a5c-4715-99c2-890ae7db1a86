import { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Search, ArrowRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: string;
  url: string;
  icon?: string;
  keywords: string[];
}

interface IntelligentSearchProps {
  className?: string;
  placeholder?: string;
}

// Comprehensive search data including all pages and common search terms
const searchData: SearchResult[] = [
  // Main Navigation Pages
  {
    id: "dashboard",
    title: "Dashboard",
    description: "Main dashboard with overview and statistics",
    category: "Navigation",
    url: "/",
    keywords: ["dashboard", "home", "overview", "main", "stats", "statistics"],
  },
  {
    id: "products",
    title: "Products",
    description: "Manage products, inventory, and catalog",
    category: "Management",
    url: "/products",
    keywords: [
      "products",
      "catalog",
      "items",
      "inventory",
      "stock",
      "medicine",
      "ayurvedic",
    ],
  },
  {
    id: "orders",
    title: "Orders",
    description: "View and manage customer orders",
    category: "Management",
    url: "/orders",
    keywords: [
      "orders",
      "purchases",
      "sales",
      "transactions",
      "pending",
      "shipped",
      "delivered",
    ],
  },
  {
    id: "customers",
    title: "Customers",
    description: "Customer management and profiles",
    category: "Management",
    url: "/customers",
    keywords: [
      "customers",
      "clients",
      "users",
      "profiles",
      "contacts",
      "active",
      "inactive",
    ],
  },
  {
    id: "inventory",
    title: "Inventory",
    description: "Stock management and tracking",
    category: "Management",
    url: "/inventory",
    keywords: [
      "inventory",
      "stock",
      "warehouse",
      "supplies",
      "tracking",
      "low stock",
    ],
  },
  {
    id: "consultant",
    title: "Consultant",
    description: "Patient management and consultations",
    category: "Medical",
    url: "/consultant",
    keywords: [
      "consultant",
      "patients",
      "appointments",
      "medical",
      "doctor",
      "consultation",
    ],
  },
  {
    id: "coupons",
    title: "Coupons & Referrals",
    description: "Manage discount coupons and referral programs",
    category: "Marketing",
    url: "/coupons",
    keywords: [
      "coupons",
      "discounts",
      "referrals",
      "promotions",
      "offers",
      "codes",
    ],
  },
  // Business Tools
  {
    id: "analytics",
    title: "Analytics",
    description: "Business analytics and reports",
    category: "Business Tools",
    url: "/analytics",
    keywords: [
      "analytics",
      "reports",
      "data",
      "insights",
      "metrics",
      "performance",
    ],
  },
  {
    id: "calendar",
    title: "Calendar",
    description: "Schedule and appointment management",
    category: "Business Tools",
    url: "/calendar",
    keywords: [
      "calendar",
      "schedule",
      "appointments",
      "events",
      "dates",
      "booking",
    ],
  },
  {
    id: "feedback",
    title: "Feedback",
    description: "Customer feedback and complaints",
    category: "Business Tools",
    url: "/feedback",
    keywords: [
      "feedback",
      "complaints",
      "reviews",
      "suggestions",
      "customer service",
    ],
  },
  {
    id: "reviews",
    title: "Reviews",
    description: "Product and service reviews",
    category: "Business Tools",
    url: "/reviews",
    keywords: ["reviews", "ratings", "testimonials", "feedback", "stars"],
  },
  {
    id: "returns",
    title: "Returns",
    description: "Return and refund management",
    category: "Business Tools",
    url: "/returns",
    keywords: ["returns", "refunds", "exchanges", "rma", "warranty"],
  },
  // Other Pages
  {
    id: "staff",
    title: "Staff Management",
    description: "Employee and staff administration",
    category: "Administration",
    url: "/staff",
    keywords: ["staff", "employees", "team", "users", "admin", "management"],
  },
  {
    id: "notifications",
    title: "Notifications",
    description: "System notifications and alerts",
    category: "System",
    url: "/notifications",
    keywords: ["notifications", "alerts", "messages", "updates", "news"],
  },
  {
    id: "settings",
    title: "Settings",
    description: "System settings and configuration",
    category: "System",
    url: "/settings",
    keywords: ["settings", "configuration", "preferences", "options", "setup"],
  },
  {
    id: "profile",
    title: "Profile",
    description: "User profile and account settings",
    category: "Account",
    url: "/profile",
    keywords: ["profile", "account", "user", "personal", "details"],
  },
  // Common search terms and actions
  {
    id: "add-product",
    title: "Add New Product",
    description: "Create a new product in the catalog",
    category: "Quick Action",
    url: "/products",
    keywords: [
      "add product",
      "new product",
      "create product",
      "add item",
      "new item",
    ],
  },
  {
    id: "view-orders",
    title: "View Recent Orders",
    description: "Check latest customer orders",
    category: "Quick Action",
    url: "/orders",
    keywords: [
      "recent orders",
      "latest orders",
      "new orders",
      "pending orders",
    ],
  },
  {
    id: "customer-list",
    title: "Customer List",
    description: "Browse all customers",
    category: "Quick Action",
    url: "/customers",
    keywords: [
      "customer list",
      "all customers",
      "browse customers",
      "customer directory",
    ],
  },
  {
    id: "low-stock",
    title: "Low Stock Items",
    description: "Check inventory with low stock",
    category: "Quick Action",
    url: "/inventory",
    keywords: [
      "low stock",
      "out of stock",
      "inventory alert",
      "stock alert",
      "reorder",
    ],
  },
  {
    id: "create-coupon",
    title: "Create Coupon",
    description: "Generate new discount coupon",
    category: "Quick Action",
    url: "/coupons",
    keywords: ["create coupon", "new coupon", "add discount", "new offer"],
  },
  {
    id: "patient-appointments",
    title: "Patient Appointments",
    description: "Manage patient appointments and consultations",
    category: "Quick Action",
    url: "/consultant",
    keywords: [
      "appointments",
      "patient appointments",
      "consultations",
      "schedule",
    ],
  },
  // Common medical/ayurvedic terms
  {
    id: "ayurvedic-products",
    title: "Ayurvedic Products",
    description: "Browse ayurvedic medicines and supplements",
    category: "Product Category",
    url: "/products",
    keywords: [
      "ayurvedic",
      "herbal",
      "natural",
      "traditional",
      "medicine",
      "herbs",
    ],
  },
  {
    id: "juice-products",
    title: "Juice Products",
    description: "Aloe vera, Amla and other health juices",
    category: "Product Category",
    url: "/products",
    keywords: [
      "juice",
      "aloe vera",
      "amla",
      "health drinks",
      "liquid supplements",
    ],
  },
  {
    id: "capsules-tablets",
    title: "Capsules & Tablets",
    description: "Capsule and tablet form medicines",
    category: "Product Category",
    url: "/products",
    keywords: ["capsules", "tablets", "pills", "ashwagandha", "supplements"],
  },
];

const IntelligentSearch: React.FC<IntelligentSearchProps> = ({
  className,
  placeholder = "Search across all data...",
}) => {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Search function with intelligent matching
  const performSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const searchResults: SearchResult[] = [];

    // Common abbreviations and shortcuts
    const abbreviations: { [key: string]: string[] } = {
      inv: ["inventory"],
      cust: ["customers", "customer"],
      prod: ["products", "product"],
      ord: ["orders", "order"],
      coup: ["coupons", "coupon"],
      cons: ["consultant", "consultation"],
      pat: ["patients", "patient"],
      app: ["appointments", "appointment"],
      med: ["medicine", "medical"],
      ayu: ["ayurvedic"],
      herb: ["herbal"],
      cap: ["capsules"],
      tab: ["tablets"],
      supp: ["supplements"],
      disc: ["discount"],
      ref: ["referral"],
      rev: ["reviews"],
      ret: ["returns"],
      ana: ["analytics"],
      cal: ["calendar"],
      set: ["settings"],
      prof: ["profile"],
      not: ["notifications"],
      staff: ["staff", "employees"],
      feed: ["feedback"],
    };

    // Expand query with abbreviations
    let expandedQueries = [query];
    Object.entries(abbreviations).forEach(([abbr, expansions]) => {
      if (query.includes(abbr)) {
        expansions.forEach((expansion) => {
          expandedQueries.push(query.replace(abbr, expansion));
        });
      }
    });

    searchData.forEach((item) => {
      let score = 0;

      expandedQueries.forEach((searchTerm) => {
        // Exact title match (highest priority)
        if (item.title.toLowerCase() === searchTerm) {
          score += 100;
        }
        // Title starts with query
        else if (item.title.toLowerCase().startsWith(searchTerm)) {
          score += 80;
        }
        // Title contains query
        else if (item.title.toLowerCase().includes(searchTerm)) {
          score += 60;
        }

        // Keyword matches
        item.keywords.forEach((keyword) => {
          if (keyword === searchTerm) {
            score += 90;
          } else if (keyword.startsWith(searchTerm)) {
            score += 70;
          } else if (keyword.includes(searchTerm)) {
            score += 40;
          }
        });

        // Description contains query
        if (item.description.toLowerCase().includes(searchTerm)) {
          score += 30;
        }

        // Category matches
        if (item.category.toLowerCase().includes(searchTerm)) {
          score += 50;
        }
      });

      if (score > 0) {
        searchResults.push({ ...item, score } as SearchResult & {
          score: number;
        });
      }
    });

    // Sort by score and limit results
    const sortedResults = searchResults
      .sort((a, b) => (b as any).score - (a as any).score)
      .slice(0, 8);

    setResults(sortedResults);
  }, []);

  // Handle input change with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [query, performSearch]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
        break;
      case "Escape":
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle result selection
  const handleResultClick = (result: SearchResult) => {
    navigate(result.url);
    setQuery("");
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  // Handle input focus/blur
  const handleFocus = () => {
    if (query.trim() && results.length > 0) {
      setIsOpen(true);
    }
  };

  const handleBlur = () => {
    // Delay closing to allow for result clicks
    setTimeout(() => {
      setIsOpen(false);
      setSelectedIndex(-1);
    }, 200);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Show dropdown when there are results
  useEffect(() => {
    setIsOpen(query.trim().length > 0 && results.length > 0);
    setSelectedIndex(-1);
  }, [results, query]);

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4 transition-colors duration-500 ease-in-out" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className="pl-10 pr-4 py-2 w-full bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 focus:ring-2 focus:ring-[#FFD700] focus:border-transparent theme-transition text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 shadow-sm"
        />
      </div>

      {/* Search Results Dropdown */}
      {isOpen && results.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto transition-colors duration-500 ease-in-out">
          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 px-3 py-2 border-b border-gray-100 dark:border-gray-700">
              Search Results ({results.length})
            </div>
            {results.map((result, index) => (
              <div
                key={result.id}
                onClick={() => handleResultClick(result)}
                className={cn(
                  "flex items-center justify-between p-3 rounded-md cursor-pointer transition-colors duration-200",
                  "hover:bg-gray-50 dark:hover:bg-gray-700",
                  selectedIndex === index && "bg-gray-50 dark:bg-gray-700"
                )}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900 dark:text-white text-sm">
                      {result.title}
                    </span>
                    <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full">
                      {result.category}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                    {result.description}
                  </p>
                </div>
                <ArrowRight className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0 ml-2" />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default IntelligentSearch;
