import React, { useState, useEffect } from "react";
import {
  Bell,
  User,
  LogOut,
  UserCircle,
  RefreshC<PERSON>,
  <PERSON>,
  Sun,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/components/theme-provider";
import { useProfile } from "@/contexts/ProfileContext";

export const Header = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const { profileData } = useProfile();

  // Check theme on component mount and listen for theme changes
  useEffect(() => {
    const checkTheme = () => {
      const isDark = document.documentElement.classList.contains("dark");
      setIsDarkMode(isDark);
    };

    checkTheme();

    // Create a MutationObserver to watch for theme changes
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("auth-token");
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account",
    });
    navigate("/auth/signin");
  };

  const handleNotificationClick = () => {
    if (location.pathname === "/notifications") {
      navigate(-1);
    } else {
      navigate("/notifications");
    }
  };

  const handleRefresh = () => {
    // Refresh functionality for dashboard
    window.location.reload();
  };

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);

    if (newTheme === "dark") {
      toast({
        title: "🌙 Dark Mode Activated",
        description: "Interface switched to dark theme",
        duration: 2000,
      });
    } else {
      toast({
        title: "☀️ Light Mode Activated",
        description: "Interface switched to light theme",
        duration: 2000,
      });
    }
  };

  return (
    <header className="flex items-center justify-end px-6 py-4 bg-white dark:bg-[#1E1E1E] transition-colors duration-500 ease-in-out">
      <div className="flex items-center gap-4">
        {/* Theme Toggle Button */}
        <Button
          variant="outline"
          size="icon"
          onClick={toggleTheme}
          className="rounded-full hover:scale-110 transition-all duration-300 ease-in-out hover:shadow-lg border-gray-300 dark:border-[#444] bg-white dark:bg-[#1E1E1E]"
          title={
            theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
          }
          aria-label={
            theme === "dark" ? "Switch to light mode" : "Switch to dark mode"
          }
        >
          {theme === "dark" ? (
            <Sun className="h-5 w-5 text-yellow-500 animate-in spin-in-180 duration-500" />
          ) : (
            <Moon className="h-5 w-5 text-gray-700 animate-in spin-in-180 duration-500" />
          )}
        </Button>
        {location.pathname === "/" && (
          <Button
            variant="outline"
            onClick={handleRefresh}
            className="text-lg px-6 py-3"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        )}
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out"
          onClick={handleNotificationClick}
        >
          <Bell className="w-5 h-5 text-gray-600 dark:text-gray-300 transition-colors duration-500 ease-in-out" />
          <Badge className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center p-0 bg-red-500 text-white text-xs">
            3
          </Badge>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium hover:opacity-80 transition-all duration-200 overflow-hidden border-2 border-gray-300 dark:border-gray-600"
              title="Profile Menu"
            >
              {profileData.profileImage ? (
                <img
                  src={profileData.profileImage}
                  alt={profileData.name}
                  className="w-full h-full object-cover rounded-full"
                />
              ) : (
                <div className="w-full h-full bg-gray-400 dark:bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500 dark:hover:bg-gray-500 transition-colors">
                  <User className="w-4 h-4 text-white" />
                </div>
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-56 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 transition-colors duration-500 ease-in-out"
          >
            <div className="flex items-center gap-2 p-2">
              <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800">
                {profileData.profileImage ? (
                  <img
                    src={profileData.profileImage}
                    alt={profileData.name}
                    className="w-full h-full object-cover object-center"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-400 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
                  {profileData.name}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 transition-colors duration-500 ease-in-out">
                  {profileData.email}
                </span>
              </div>
            </div>
            <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
            <DropdownMenuItem asChild>
              <Link
                to="/settings"
                className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out"
              >
                <UserCircle className="w-4 h-4" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
            <DropdownMenuItem
              onClick={handleLogout}
              className="flex items-center gap-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300 ease-in-out"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
