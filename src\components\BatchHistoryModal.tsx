
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface BatchHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
}

const batchData = [
  {
    batchId: "BATCH-001",
    quantity: 100,
    purchaseDate: "2024-01-15",
    expiryDate: "2025-01-15",
    supplier: "ABC Distributors",
    status: "Active",
    remaining: 75
  },
  {
    batchId: "BATCH-002", 
    quantity: 50,
    purchaseDate: "2024-01-10",
    expiryDate: "2025-01-10",
    supplier: "Health Products Ltd",
    status: "Active",
    remaining: 25
  }
];

const BatchHistoryModal = ({ isOpen, onClose, productName }: BatchHistoryModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Batch History - {productName}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Batch ID</TableHead>
                <TableHead>Purchase Date</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Original Qty</TableHead>
                <TableHead>Remaining</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {batchData.map((batch) => (
                <TableRow key={batch.batchId}>
                  <TableCell className="font-medium">{batch.batchId}</TableCell>
                  <TableCell>{batch.purchaseDate}</TableCell>
                  <TableCell>{batch.expiryDate}</TableCell>
                  <TableCell>{batch.supplier}</TableCell>
                  <TableCell>{batch.quantity}</TableCell>
                  <TableCell>{batch.remaining}</TableCell>
                  <TableCell>
                    <Badge 
                      variant="secondary"
                      className="bg-green-100 text-green-700"
                    >
                      {batch.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BatchHistoryModal;
