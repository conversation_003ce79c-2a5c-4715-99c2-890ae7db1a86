import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import {
  PRODUCT_CATEGORIES,
  PRODUCTS_LIST,
  isValidCategory,
  isValidProduct,
  getCategoryForProduct,
} from "@/constants/products";

interface CreateCouponModalProps {
  isOpen: boolean;
  onClose: () => void;
  coupon?: any;
  onSave: (couponData: any) => void;
}

type ControlType = "multiple" | "single" | null;
type MultipleControlOption =
  | "product-customer"
  | "customer-category"
  | "product-category"
  | null;
type SingleControlOption =
  | "single-product"
  | "single-category"
  | "single-customer"
  | null;

const CreateCouponModal = ({
  isOpen,
  onClose,
  coupon,
  onSave,
}: CreateCouponModalProps) => {
  const { toast } = useToast();

  // Modal step management
  const [currentStep, setCurrentStep] = useState<"control-selection" | "form">(
    "control-selection"
  );
  const [controlType, setControlType] = useState<ControlType>(null);
  const [multipleControlOption, setMultipleControlOption] =
    useState<MultipleControlOption>(null);
  const [singleControlOption, setSingleControlOption] =
    useState<SingleControlOption>(null);

  // Form data
  const [formData, setFormData] = useState({
    code: "",
    description: "",
    productName: "",
    category: "",
    discountType: "percentage",
    discountValue: "",
    minOrder: "",
    maxDiscount: "",
    usageLimit: "",
    validFrom: "",
    validTo: "",
    status: "Active",
  });

  // Reset modal state when opening/closing
  useEffect(() => {
    if (isOpen) {
      if (coupon) {
        // Editing existing coupon - skip control selection
        setCurrentStep("form");
        setFormData({
          code: coupon.code || "",
          description: coupon.description || "",
          productName: coupon.productName || "",
          category: coupon.category || "",
          discountType: coupon.type === "Percentage" ? "percentage" : "fixed",
          discountValue: coupon.value?.toString() || "",
          minOrder: coupon.minOrder?.toString() || "",
          maxDiscount: coupon.maxDiscount?.toString() || "",
          usageLimit: coupon.usageLimit?.toString() || "",
          validFrom: coupon.validFrom || "",
          validTo: coupon.validTo || "",
          status: coupon.status || "Active",
        });
      } else {
        // Creating new coupon - start with control selection
        setCurrentStep("control-selection");
        setControlType(null);
        setMultipleControlOption(null);
        setSingleControlOption(null);
        setFormData({
          code: "",
          description: "",
          productName: "",
          category: "",
          discountType: "percentage",
          discountValue: "",
          minOrder: "",
          maxDiscount: "",
          usageLimit: "",
          validFrom: "",
          validTo: "",
          status: "Active",
        });
      }
    }
  }, [isOpen, coupon]);

  // Control selection handlers
  const handleControlTypeSelect = (type: ControlType) => {
    setControlType(type);
    setMultipleControlOption(null);
    setSingleControlOption(null);
  };

  const handleMultipleControlSelect = (option: MultipleControlOption) => {
    setMultipleControlOption(option);
    setCurrentStep("form");
  };

  const handleSingleControlSelect = (option: SingleControlOption) => {
    setSingleControlOption(option);
    setCurrentStep("form");
  };

  const handleBackToControlSelection = () => {
    setCurrentStep("control-selection");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !formData.code ||
      !formData.productName ||
      !formData.discountType ||
      !formData.discountValue ||
      !formData.usageLimit ||
      !formData.validTo
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Validate product name exists in our product list
    if (!isValidProduct(formData.productName)) {
      toast({
        title: "Invalid Product",
        description: "Please select a valid product from the list",
        variant: "destructive",
      });
      return;
    }

    // Validate category if provided
    if (formData.category && !isValidCategory(formData.category)) {
      toast({
        title: "Invalid Category",
        description: "Please select a valid category",
        variant: "destructive",
      });
      return;
    }

    // Auto-set category based on product if not provided
    const productCategory = getCategoryForProduct(formData.productName);
    const finalCategory = formData.category || productCategory || "";

    const couponData = {
      ...formData,
      category: finalCategory,
      type: formData.discountType === "percentage" ? "Percentage" : "Fixed",
      value: parseFloat(formData.discountValue),
      minOrder: formData.minOrder ? parseFloat(formData.minOrder) : 0,
      maxDiscount: formData.maxDiscount ? parseFloat(formData.maxDiscount) : 0,
      usageLimit: parseInt(formData.usageLimit),
      id: coupon?.id || Date.now(),
      used: coupon?.used || 0,
    };

    onSave(couponData);

    toast({
      title: "Success",
      description: coupon
        ? "Coupon updated successfully"
        : "Coupon created successfully",
    });

    onClose();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const renderControlSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Select Control Type
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Choose how you want to configure your coupon
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Multiple Control */}
        <div className="space-y-4">
          <Button
            type="button"
            variant={controlType === "multiple" ? "default" : "outline"}
            className={`w-full h-16 text-lg font-medium transition-all duration-300 ${
              controlType === "multiple"
                ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
                : "border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400"
            }`}
            onClick={() => handleControlTypeSelect("multiple")}
          >
            Multiple Control
          </Button>

          {controlType === "multiple" && (
            <div className="space-y-3 animate-in slide-in-from-top-2 duration-300">
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  multipleControlOption === "product-customer"
                    ? "bg-blue-50 border-blue-500 text-blue-700 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleMultipleControlSelect("product-customer")}
              >
                Product & Customer
              </Button>
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  multipleControlOption === "customer-category"
                    ? "bg-blue-50 border-blue-500 text-blue-700 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleMultipleControlSelect("customer-category")}
              >
                Customer & Category
              </Button>
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  multipleControlOption === "product-category"
                    ? "bg-blue-50 border-blue-500 text-blue-700 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleMultipleControlSelect("product-category")}
              >
                Product & Category
              </Button>
            </div>
          )}
        </div>

        {/* Single Control */}
        <div className="space-y-4">
          <Button
            type="button"
            variant={controlType === "single" ? "default" : "outline"}
            className={`w-full h-16 text-lg font-medium transition-all duration-300 ${
              controlType === "single"
                ? "bg-green-600 hover:bg-green-700 text-white shadow-lg"
                : "border-2 border-gray-300 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-400"
            }`}
            onClick={() => handleControlTypeSelect("single")}
          >
            Single Control
          </Button>

          {controlType === "single" && (
            <div className="space-y-3 animate-in slide-in-from-top-2 duration-300">
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  singleControlOption === "single-product"
                    ? "bg-green-50 border-green-500 text-green-700 dark:bg-green-900/20 dark:border-green-400 dark:text-green-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleSingleControlSelect("single-product")}
              >
                Single Product
              </Button>
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  singleControlOption === "single-category"
                    ? "bg-green-50 border-green-500 text-green-700 dark:bg-green-900/20 dark:border-green-400 dark:text-green-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleSingleControlSelect("single-category")}
              >
                Single Category
              </Button>
              <Button
                type="button"
                variant="outline"
                className={`w-full h-12 transition-all duration-300 ${
                  singleControlOption === "single-customer"
                    ? "bg-green-50 border-green-500 text-green-700 dark:bg-green-900/20 dark:border-green-400 dark:text-green-300 shadow-md"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => handleSingleControlSelect("single-customer")}
              >
                Single Customer
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-2xl text-gray-900 dark:text-white transition-colors duration-500 ease-in-out">
            {coupon ? "Edit Coupon" : "Create New Coupon"}
          </DialogTitle>
        </DialogHeader>

        {currentStep === "control-selection" ? (
          renderControlSelection()
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Back button for form step */}
            {!coupon && (
              <Button
                type="button"
                variant="ghost"
                onClick={handleBackToControlSelection}
                className="mb-4 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
              >
                ← Back to Control Selection
              </Button>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="code"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Coupon Code *
                </Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  placeholder="Enter coupon code"
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="productName"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Product Name *
                </Label>
                <Select
                  value={formData.productName}
                  onValueChange={(value) => {
                    handleInputChange("productName", value);
                    // Auto-set category when product is selected
                    const productCategory = getCategoryForProduct(value);
                    if (productCategory) {
                      handleInputChange("category", productCategory);
                    }
                  }}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Select product" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRODUCTS_LIST.map((product) => (
                      <SelectItem key={product.id} value={product.name}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="description"
                className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
              >
                Description
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="Enter coupon description"
                className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2 min-h-[80px]"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="category"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Category
                </Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    handleInputChange("category", value)
                  }
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRODUCT_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out">
                  Discount Type *
                </Label>
                <RadioGroup
                  value={formData.discountType}
                  onValueChange={(value) =>
                    handleInputChange("discountType", value)
                  }
                  className="flex gap-6 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="percentage" id="percentage" />
                    <Label
                      htmlFor="percentage"
                      className="text-base cursor-pointer"
                    >
                      Percentage
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fixed" id="fixed" />
                    <Label htmlFor="fixed" className="text-base cursor-pointer">
                      Fixed Amount
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="discountValue"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  {formData.discountType === "percentage"
                    ? "Discount Percentage *"
                    : "Discount Amount *"}
                </Label>
                <Input
                  id="discountValue"
                  type="number"
                  value={formData.discountValue}
                  onChange={(e) =>
                    handleInputChange("discountValue", e.target.value)
                  }
                  placeholder={
                    formData.discountType === "percentage"
                      ? "Enter percentage (e.g., 10)"
                      : "Enter amount (e.g., 100)"
                  }
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="minOrder"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Minimum Order Amount
                </Label>
                <Input
                  id="minOrder"
                  type="number"
                  value={formData.minOrder}
                  onChange={(e) =>
                    handleInputChange("minOrder", e.target.value)
                  }
                  placeholder="Enter minimum order amount"
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="maxDiscount"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Maximum Discount
                </Label>
                <Input
                  id="maxDiscount"
                  type="number"
                  value={formData.maxDiscount}
                  onChange={(e) =>
                    handleInputChange("maxDiscount", e.target.value)
                  }
                  placeholder="Enter maximum discount"
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="usageLimit"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Usage Limit *
                </Label>
                <Input
                  id="usageLimit"
                  type="number"
                  value={formData.usageLimit}
                  onChange={(e) =>
                    handleInputChange("usageLimit", e.target.value)
                  }
                  placeholder="Enter usage limit"
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="validFrom"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Valid From
                </Label>
                <Input
                  id="validFrom"
                  type="date"
                  value={formData.validFrom}
                  onChange={(e) =>
                    handleInputChange("validFrom", e.target.value)
                  }
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="validTo"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Valid To *
                </Label>
                <Input
                  id="validTo"
                  type="date"
                  value={formData.validTo}
                  onChange={(e) => handleInputChange("validTo", e.target.value)}
                  className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base px-4 py-2"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="status"
                  className="text-lg font-medium text-gray-700 dark:text-gray-300 transition-colors duration-500 ease-in-out"
                >
                  Status *
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger className="transition-colors duration-500 ease-in-out bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="px-6 py-2 text-base transition-colors duration-500 ease-in-out"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#FFD700] hover:bg-[#E6C200] text-black text-base px-6 py-2 transition-colors duration-300 ease-in-out"
              >
                {coupon ? "Update Coupon" : "Create Coupon"}
              </Button>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateCouponModal;
