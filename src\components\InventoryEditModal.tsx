
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

interface Product {
  id: number;
  product: string;
  sku: string;
  category: string;
  currentStock: number;
  minStock: number;
  stockLevel: string;
  value: string;
  price: string;
  lastRestocked: string;
  status: string;
  batches: string;
}

interface InventoryEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  onUpdate: (product: Product) => void;
}

const InventoryEditModal: React.FC<InventoryEditModalProps> = ({ 
  isOpen, 
  onClose, 
  product, 
  onUpdate 
}) => {
  const [formData, setFormData] = useState<Product | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (product) {
      setFormData({ ...product });
    }
  }, [product]);

  if (!product || !formData) return null;

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleSubmit = () => {
    if (!formData) return;

    // Calculate new stock level based on current stock
    const stockPercentage = Math.round((formData.currentStock / (formData.currentStock + 50)) * 100);
    const updatedProduct = {
      ...formData,
      stockLevel: `${stockPercentage}% capacity`,
      value: `₹${(formData.currentStock * parseInt(formData.price.replace('₹', ''))).toLocaleString()}`
    };

    onUpdate(updatedProduct);
    onClose();
    toast({
      title: "Product Updated",
      description: "Product details have been successfully updated",
    });
  };

  const handleClose = () => {
    setFormData(product);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Edit Product - {product.product}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="product" className="text-lg">Product Name</Label>
              <Input
                id="product"
                value={formData.product}
                onChange={(e) => handleInputChange('product', e.target.value)}
                className="text-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="sku" className="text-lg">SKU</Label>
              <Input
                id="sku"
                value={formData.sku}
                disabled
                className="bg-gray-100 dark:bg-gray-700 text-lg"
              />
            </div>
          </div>

          {/* Stock Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="currentStock" className="text-lg">Current Stock</Label>
              <Input
                id="currentStock"
                type="number"
                value={formData.currentStock}
                onChange={(e) => handleInputChange('currentStock', parseInt(e.target.value))}
                className="text-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minStock" className="text-lg">Minimum Stock</Label>
              <Input
                id="minStock"
                type="number"
                value={formData.minStock}
                onChange={(e) => handleInputChange('minStock', parseInt(e.target.value))}
                className="text-lg"
              />
            </div>
          </div>

          {/* Price and Category */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price" className="text-lg">Price</Label>
              <Input
                id="price"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className="text-lg"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category" className="text-lg">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger className="text-lg">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Health Drinks">Health Drinks</SelectItem>
                  <SelectItem value="Supplements">Supplements</SelectItem>
                  <SelectItem value="Combo Packs">Combo Packs</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Status and Last Restocked */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status" className="text-lg">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="text-lg">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in stock">In Stock</SelectItem>
                  <SelectItem value="low stock">Low Stock</SelectItem>
                  <SelectItem value="out of stock">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastRestocked" className="text-lg">Last Restocked</Label>
              <Input
                id="lastRestocked"
                type="date"
                value={formData.lastRestocked}
                onChange={(e) => handleInputChange('lastRestocked', e.target.value)}
                className="text-lg"
              />
            </div>
          </div>

          {/* Current Values Display */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Current Values</h3>
            <div className="grid grid-cols-2 gap-4 text-base">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Current Value: </span>
                <span className="font-medium text-gray-900 dark:text-white">{formData.value}</span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Stock Level: </span>
                <span className="font-medium text-gray-900 dark:text-white">{formData.stockLevel}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={handleClose}
            className="text-lg px-6 py-3"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            className="bg-blue-600 hover:bg-blue-700 text-lg px-6 py-3"
          >
            Update Product
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryEditModal;
